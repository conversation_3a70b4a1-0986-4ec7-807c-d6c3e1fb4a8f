@echo off
REM VIKKI AI Deployment Script for Windows
REM This script helps deploy the application in different environments

setlocal enabledelayedexpansion

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed. Please install Docker Desktop first.
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose is not installed. Please install Docker Compose first.
    exit /b 1
)

REM Setup environment file
if not exist .env (
    if exist .env.example (
        copy .env.example .env >nul
        echo [WARNING] Created .env file from .env.example. Please update it with your values.
    ) else (
        echo [ERROR] .env.example file not found. Please create .env file manually.
        exit /b 1
    )
) else (
    echo [INFO] .env file already exists
)

REM Parse command line arguments
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=help

if "%COMMAND%"=="dev" goto :deploy_dev
if "%COMMAND%"=="development" goto :deploy_dev
if "%COMMAND%"=="prod" goto :deploy_prod
if "%COMMAND%"=="production" goto :deploy_prod
if "%COMMAND%"=="logs" goto :show_logs
if "%COMMAND%"=="stop" goto :stop_services
if "%COMMAND%"=="health" goto :health_check
if "%COMMAND%"=="cleanup" goto :cleanup
goto :show_help

:deploy_dev
echo [INFO] Deploying in development mode...
docker-compose down
docker-compose up -d --build
if errorlevel 1 (
    echo [ERROR] Development deployment failed
    exit /b 1
)
echo [SUCCESS] Development deployment completed
echo [INFO] Services available at:
echo   - Frontend: http://localhost
echo   - Backend API: http://localhost:8000
echo   - MongoDB: localhost:27017
goto :end

:deploy_prod
echo [INFO] Deploying in production mode...
if not exist ssl\fullchain.pem (
    echo [WARNING] SSL certificates not found in ssl\ directory
    echo [WARNING] HTTPS will not work properly. Please add SSL certificates.
)
docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
docker-compose -f docker-compose.yml -f docker-compose.prod.yml pull
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build
if errorlevel 1 (
    echo [ERROR] Production deployment failed
    exit /b 1
)
echo [SUCCESS] Production deployment completed
goto :end

:show_logs
if "%2"=="" (
    echo [INFO] Showing logs for all services...
    docker-compose logs -f
) else (
    echo [INFO] Showing logs for %2...
    docker-compose logs -f %2
)
goto :end

:stop_services
echo [INFO] Stopping all services...
docker-compose down
echo [SUCCESS] All services stopped
goto :end

:health_check
echo [INFO] Performing health check...

REM Check if containers are running
docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    echo [ERROR] No containers are running
    goto :end
)

REM Check backend health (using PowerShell for HTTP request)
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:8000/health' -UseBasicParsing | Out-Null; Write-Host '[SUCCESS] Backend is healthy' } catch { Write-Host '[ERROR] Backend health check failed' }"

REM Check frontend
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost' -UseBasicParsing | Out-Null; Write-Host '[SUCCESS] Frontend is accessible' } catch { Write-Host '[ERROR] Frontend is not accessible' }"
goto :end

:cleanup
echo [INFO] Cleaning up Docker resources...
docker-compose down
docker image prune -f
echo [WARNING] To remove unused volumes (this will delete data), run: docker volume prune -f
echo [SUCCESS] Cleanup completed
goto :end

:show_help
echo VIKKI AI Deployment Script for Windows
echo.
echo Usage: %0 [COMMAND]
echo.
echo Commands:
echo   dev         Deploy in development mode
echo   prod        Deploy in production mode
echo   logs        Show logs for all services
echo   logs ^<svc^>  Show logs for specific service
echo   stop        Stop all services
echo   health      Perform health check
echo   cleanup     Clean up Docker resources
echo   help        Show this help message
echo.
echo Examples:
echo   %0 dev                 # Deploy in development
echo   %0 prod                # Deploy in production
echo   %0 logs backend        # Show backend logs
echo   %0 health              # Check service health
goto :end

:end
endlocal
