import * as vscode from 'vscode';
import { VikkiAIProvider } from './VikkiAIProvider';

export class Chat<PERSON>iewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'vikkiAI.chatView';
    private _view?: vscode.WebviewView;

    constructor(
        private readonly _context: vscode.ExtensionContext,
        private readonly _vikkiProvider: VikkiAIProvider
    ) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._context.extensionUri
            ]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(
            async (data) => {
                switch (data.type) {
                    case 'sendMessage':
                        await this.handleChatMessage(data.message);
                        break;
                    case 'clearChat':
                        this.clearChat();
                        break;
                    case 'insertCode':
                        await this.insertCodeIntoEditor(data.code);
                        break;
                }
            },
            undefined,
            this._context.subscriptions
        );
    }

    private async handleChatMessage(message: string) {
        if (!this._view) {
            return;
        }

        try {
            // Add user message to chat
            this._view.webview.postMessage({
                type: 'addMessage',
                message: {
                    role: 'user',
                    content: message,
                    timestamp: new Date().toISOString()
                }
            });

            // Show typing indicator
            this._view.webview.postMessage({
                type: 'showTyping'
            });

            // Get context from current editor
            const context = this.getCurrentEditorContext();

            // Send message to VIKKI AI
            const response = await this._vikkiProvider.chat({
                message: message,
                context: context
            });

            // Hide typing indicator
            this._view.webview.postMessage({
                type: 'hideTyping'
            });

            // Add AI response to chat
            this._view.webview.postMessage({
                type: 'addMessage',
                message: {
                    role: 'assistant',
                    content: response.response,
                    timestamp: new Date().toISOString()
                }
            });

        } catch (error: any) {
            // Hide typing indicator
            this._view.webview.postMessage({
                type: 'hideTyping'
            });

            // Show error message
            this._view.webview.postMessage({
                type: 'addMessage',
                message: {
                    role: 'error',
                    content: `Error: ${error.message}`,
                    timestamp: new Date().toISOString()
                }
            });
        }
    }

    private clearChat() {
        if (this._view) {
            this._view.webview.postMessage({
                type: 'clearMessages'
            });
        }
    }

    private async insertCodeIntoEditor(code: string) {
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const position = editor.selection.active;
            await editor.edit(editBuilder => {
                editBuilder.insert(position, code);
            });
            vscode.window.showInformationMessage('Code inserted successfully!');
        } else {
            // Create new file if no editor is open
            const doc = await vscode.workspace.openTextDocument({
                content: code,
                language: 'javascript'
            });
            await vscode.window.showTextDocument(doc);
        }
    }

    private getCurrentEditorContext(): string {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return '';
        }

        const document = editor.document;
        let context = `Current file: ${document.fileName}\n`;
        context += `Language: ${document.languageId}\n`;

        // Add selection if any
        const selection = editor.selection;
        if (!selection.isEmpty) {
            const selectedText = document.getText(selection);
            context += `\nSelected code:\n\`\`\`\n${selectedText}\n\`\`\`\n`;
        }

        return context;
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>VIKKI AI Chat</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    margin: 0;
                    padding: 10px;
                    height: 100vh;
                    display: flex;
                    flex-direction: column;
                }
                
                .chat-container {
                    flex: 1;
                    overflow-y: auto;
                    margin-bottom: 10px;
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 4px;
                    padding: 10px;
                }
                
                .message {
                    margin-bottom: 15px;
                    padding: 8px 12px;
                    border-radius: 8px;
                    max-width: 90%;
                }
                
                .message.user {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    margin-left: auto;
                    text-align: right;
                }
                
                .message.assistant {
                    background-color: var(--vscode-editor-selectionBackground);
                    border-left: 3px solid var(--vscode-button-background);
                }
                
                .message.error {
                    background-color: var(--vscode-inputValidation-errorBackground);
                    border-left: 3px solid var(--vscode-inputValidation-errorBorder);
                }
                
                .message-content {
                    white-space: pre-wrap;
                    word-wrap: break-word;
                }
                
                .message-time {
                    font-size: 0.8em;
                    opacity: 0.7;
                    margin-top: 5px;
                }
                
                .typing-indicator {
                    display: none;
                    padding: 10px;
                    font-style: italic;
                    opacity: 0.7;
                }
                
                .input-container {
                    display: flex;
                    gap: 5px;
                    margin-top: auto;
                }
                
                .message-input {
                    flex: 1;
                    padding: 8px;
                    border: 1px solid var(--vscode-input-border);
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border-radius: 4px;
                    font-family: inherit;
                    font-size: inherit;
                }
                
                .send-button, .clear-button {
                    padding: 8px 12px;
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-family: inherit;
                }
                
                .send-button:hover, .clear-button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                
                .clear-button {
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                }
                
                .code-block {
                    background-color: var(--vscode-textCodeBlock-background);
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 4px;
                    padding: 10px;
                    margin: 10px 0;
                    font-family: var(--vscode-editor-font-family);
                    position: relative;
                }
                
                .code-insert-btn {
                    position: absolute;
                    top: 5px;
                    right: 5px;
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 3px;
                    padding: 4px 8px;
                    font-size: 0.8em;
                    cursor: pointer;
                }
                
                .welcome-message {
                    text-align: center;
                    padding: 20px;
                    opacity: 0.7;
                }
            </style>
        </head>
        <body>
            <div class="chat-container" id="chatContainer">
                <div class="welcome-message">
                    <h3>🤖 VIKKI AI Chat</h3>
                    <p>Ask me anything about your code, request explanations, or get help with programming!</p>
                </div>
            </div>
            
            <div class="typing-indicator" id="typingIndicator">
                VIKKI AI is typing...
            </div>
            
            <div class="input-container">
                <input type="text" id="messageInput" class="message-input" placeholder="Ask VIKKI AI anything..." />
                <button id="sendButton" class="send-button">Send</button>
                <button id="clearButton" class="clear-button">Clear</button>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                const chatContainer = document.getElementById('chatContainer');
                const messageInput = document.getElementById('messageInput');
                const sendButton = document.getElementById('sendButton');
                const clearButton = document.getElementById('clearButton');
                const typingIndicator = document.getElementById('typingIndicator');

                function sendMessage() {
                    const message = messageInput.value.trim();
                    if (message) {
                        vscode.postMessage({
                            type: 'sendMessage',
                            message: message
                        });
                        messageInput.value = '';
                    }
                }

                function clearChat() {
                    vscode.postMessage({
                        type: 'clearChat'
                    });
                }

                function addMessage(message) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = \`message \${message.role}\`;
                    
                    let content = message.content;
                    
                    // Process code blocks
                    content = content.replace(/\`\`\`([\\s\\S]*?)\`\`\`/g, (match, code) => {
                        return \`<div class="code-block">
                            <button class="code-insert-btn" onclick="insertCode('\${code.replace(/'/g, "\\\\'")}')">Insert</button>
                            <pre><code>\${code}</code></pre>
                        </div>\`;
                    });
                    
                    messageDiv.innerHTML = \`
                        <div class="message-content">\${content}</div>
                        <div class="message-time">\${new Date(message.timestamp).toLocaleTimeString()}</div>
                    \`;
                    
                    chatContainer.appendChild(messageDiv);
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }

                function insertCode(code) {
                    vscode.postMessage({
                        type: 'insertCode',
                        code: code
                    });
                }

                function clearMessages() {
                    chatContainer.innerHTML = \`
                        <div class="welcome-message">
                            <h3>🤖 VIKKI AI Chat</h3>
                            <p>Ask me anything about your code, request explanations, or get help with programming!</p>
                        </div>
                    \`;
                }

                function showTyping() {
                    typingIndicator.style.display = 'block';
                }

                function hideTyping() {
                    typingIndicator.style.display = 'none';
                }

                // Event listeners
                sendButton.addEventListener('click', sendMessage);
                clearButton.addEventListener('click', clearChat);
                
                messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });

                // Handle messages from extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    switch (message.type) {
                        case 'addMessage':
                            addMessage(message.message);
                            break;
                        case 'clearMessages':
                            clearMessages();
                            break;
                        case 'showTyping':
                            showTyping();
                            break;
                        case 'hideTyping':
                            hideTyping();
                            break;
                    }
                });
            </script>
        </body>
        </html>`;
    }
}
