import * as vscode from 'vscode';
import { AuthenticationManager } from '../services/AuthenticationManager';

export interface GenerateCodeRequest {
    prompt: string;
    model?: string;
    language?: string;
    context?: string;
}

export interface GenerateCodeResponse {
    code?: string;
    message?: string;
    project_id?: string;
    files?: string[];
    download_url?: string;
    preview_url?: string;
}

export interface ChatRequest {
    message: string;
    model?: string;
    context?: string;
}

export interface ChatResponse {
    response: string;
    model?: string;
}

export interface AnalyzeProjectRequest {
    files: Array<{
        path: string;
        content: string;
    }>;
}

export interface AnalyzeProjectResponse {
    analysis: any;
    suggestions: string[];
    issues: string[];
    metrics: any;
}

export class VikkiAIProvider {
    private axios: any;
    private apiEndpoint: string;

    constructor(private authManager: AuthenticationManager) {
        this.axios = require('axios');
        const config = vscode.workspace.getConfiguration('vikkiAI');
        this.apiEndpoint = config.get<string>('apiEndpoint', 'https://vikki-ai.com/api');
    }

    private async getHeaders(): Promise<{ [key: string]: string }> {
        const apiKey = await this.authManager.getApiKey();
        if (!apiKey) {
            throw new Error('Not authenticated. Please set your API key first.');
        }

        return {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'User-Agent': 'VIKKI-AI-VSCode-Extension/1.0.0'
        };
    }

    async generateCode(request: GenerateCodeRequest): Promise<GenerateCodeResponse> {
        try {
            const headers = await this.getHeaders();
            
            const response = await this.axios.post(
                `${this.apiEndpoint}/generate`,
                {
                    prompt: request.prompt,
                    model: request.model || 'vikki',
                    language: request.language,
                    context: request.context
                },
                { 
                    headers,
                    timeout: 60000 // 60 seconds timeout
                }
            );

            return response.data;
        } catch (error: any) {
            this.handleError(error);
            throw error;
        }
    }

    async generateCodeStreaming(request: GenerateCodeRequest): Promise<AsyncIterable<any>> {
        try {
            const headers = await this.getHeaders();
            
            const response = await this.axios.post(
                `${this.apiEndpoint}/stream-files`,
                {
                    prompt: request.prompt,
                    model: request.model || 'vikki'
                },
                {
                    headers,
                    responseType: 'stream',
                    timeout: 120000 // 2 minutes timeout for streaming
                }
            );

            return this.parseSSEStream(response.data);
        } catch (error: any) {
            this.handleError(error);
            throw error;
        }
    }

    async chat(request: ChatRequest): Promise<ChatResponse> {
        try {
            const headers = await this.getHeaders();
            
            const response = await this.axios.post(
                `${this.apiEndpoint}/chat`,
                {
                    message: request.message,
                    model: request.model || 'vikki',
                    context: request.context
                },
                { 
                    headers,
                    timeout: 30000 // 30 seconds timeout
                }
            );

            return response.data;
        } catch (error: any) {
            this.handleError(error);
            throw error;
        }
    }

    async analyzeProject(request: AnalyzeProjectRequest): Promise<AnalyzeProjectResponse> {
        try {
            const headers = await this.getHeaders();
            
            // Create FormData for file upload
            const FormData = require('form-data');
            const formData = new FormData();
            
            // Add files to form data
            request.files.forEach((file, index) => {
                const buffer = Buffer.from(file.content, 'utf8');
                formData.append('files', buffer, {
                    filename: file.path,
                    contentType: 'text/plain'
                });
            });

            const response = await this.axios.post(
                `${this.apiEndpoint}/analyze-project`,
                formData,
                {
                    headers: {
                        ...headers,
                        ...formData.getHeaders()
                    },
                    timeout: 60000 // 60 seconds timeout
                }
            );

            return response.data;
        } catch (error: any) {
            this.handleError(error);
            throw error;
        }
    }

    async getUserStats(): Promise<any> {
        try {
            const headers = await this.getHeaders();
            
            const response = await this.axios.get(
                `${this.apiEndpoint}/users/stats`,
                { headers }
            );

            return response.data;
        } catch (error: any) {
            this.handleError(error);
            throw error;
        }
    }

    async getUserProjects(): Promise<any> {
        try {
            const headers = await this.getHeaders();
            
            const response = await this.axios.get(
                `${this.apiEndpoint}/users/projects`,
                { headers }
            );

            return response.data;
        } catch (error: any) {
            this.handleError(error);
            throw error;
        }
    }

    async downloadProject(projectId: string): Promise<Buffer> {
        try {
            const headers = await this.getHeaders();
            
            const response = await this.axios.get(
                `${this.apiEndpoint}/projects/${projectId}/download`,
                { 
                    headers,
                    responseType: 'arraybuffer'
                }
            );

            return Buffer.from(response.data);
        } catch (error: any) {
            this.handleError(error);
            throw error;
        }
    }

    private async *parseSSEStream(stream: any): AsyncIterable<any> {
        let buffer = '';
        
        for await (const chunk of stream) {
            buffer += chunk.toString();
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        yield data;
                    } catch (error) {
                        console.error('Failed to parse SSE data:', error);
                    }
                }
            }
        }
    }

    private handleError(error: any): void {
        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.detail || error.response.data?.message || 'Unknown error';
            
            switch (status) {
                case 401:
                    vscode.window.showErrorMessage(
                        'Authentication failed. Please check your API key.',
                        'Set API Key'
                    ).then(choice => {
                        if (choice === 'Set API Key') {
                            vscode.commands.executeCommand('vikkiAI.authenticate');
                        }
                    });
                    break;
                case 402:
                    vscode.window.showErrorMessage(
                        'Insufficient tokens. Please upgrade your plan.',
                        'Upgrade Plan'
                    ).then(choice => {
                        if (choice === 'Upgrade Plan') {
                            vscode.env.openExternal(vscode.Uri.parse('https://vikki-ai.com/pricing'));
                        }
                    });
                    break;
                case 429:
                    vscode.window.showErrorMessage('Rate limit exceeded. Please try again later.');
                    break;
                case 500:
                    vscode.window.showErrorMessage(`Server error: ${message}`);
                    break;
                default:
                    vscode.window.showErrorMessage(`API error (${status}): ${message}`);
            }
        } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
            vscode.window.showErrorMessage(
                'Cannot connect to VIKKI AI servers. Please check your internet connection.',
                'Retry'
            );
        } else if (error.code === 'ECONNABORTED') {
            vscode.window.showErrorMessage('Request timeout. Please try again.');
        } else {
            vscode.window.showErrorMessage(`Network error: ${error.message}`);
        }
    }
}
