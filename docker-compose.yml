version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: vikki-ai-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: vikki-ai
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - vikki-ai-network

  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: vikki-ai-backend
    restart: unless-stopped
    environment:
      - MONGODB_URI=*******************************************************************
      - JWT_SECRET=5454asdf5454asd
      - JWT_REFRESH_SECRET=5454asdf5454asd
      - PORT=8000
      - RAZORPAY_KEY_ID=${RAZORPAY_KEY_ID:-your_razorpay_key_id_here}
      - RAZORPAY_KEY_SECRET=${RAZORPAY_KEY_SECRET:-your_razorpay_key_secret_here}
      - RAZORPAY_WEBHOOK_SECRET=${RAZORPAY_WEBHOOK_SECRET:-your_razorpay_webhook_secret_here}
      - CURRENCY=USD
      - PAYMENT_SUCCESS_URL=https://vikki-ai.com/payment/success
      - PAYMENT_CANCEL_URL=https://vikki-ai.com/payment/cancel
      - STABILITY_API_KEY=${STABILITY_API_KEY:-}
      - SD_API_URL=https://api.stability.ai
    ports:
      - "8000:8000"
    volumes:
      - ./backend/generated_projects:/app/generated_projects
      - ./backend/packaged_projects:/app/packaged_projects
      - ./backend/uploads:/app/uploads
      - ./backend/preview_instances:/app/preview_instances
    depends_on:
      - mongodb
    networks:
      - vikki-ai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: vikki-ai-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - vikki-ai-network
    volumes:
      - ./ssl:/etc/nginx/ssl:ro  # Mount SSL certificates if available

volumes:
  mongodb_data:
    driver: local

networks:
  vikki-ai-network:
    driver: bridge
