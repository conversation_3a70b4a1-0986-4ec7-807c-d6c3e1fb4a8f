import * as vscode from 'vscode';
import * as path from 'path';
import { VikkiAIProvider } from '../providers/VikkiAIProvider';

export class CodeGenerator {
    constructor(private vikkiProvider: VikkiAIProvider) {}

    async generateCode(prompt: string): Promise<void> {
        const progressOptions = {
            location: vscode.ProgressLocation.Notification,
            title: "VIKKI AI",
            cancellable: true
        };

        await vscode.window.withProgress(progressOptions, async (progress, token) => {
            try {
                progress.report({ message: "Generating code..." });

                // Get current workspace and file context
                const context = await this.getCurrentContext();
                
                const response = await this.vikkiProvider.generateCode({
                    prompt,
                    context: context,
                    language: this.detectLanguage()
                });

                if (token.isCancellationRequested) {
                    return;
                }

                progress.report({ message: "Processing response..." });

                if (response.code) {
                    // Single file response
                    await this.insertGeneratedCode(response.code);
                } else if (response.files && response.files.length > 0) {
                    // Multi-file project response
                    await this.handleProjectGeneration(response);
                } else {
                    vscode.window.showInformationMessage(
                        response.message || 'Code generated successfully!'
                    );
                }

            } catch (error: any) {
                if (!token.isCancellationRequested) {
                    vscode.window.showErrorMessage(`Code generation failed: ${error.message}`);
                }
            }
        });
    }

    async generateFromSelection(selectedText: string, prompt: string, editor: vscode.TextEditor): Promise<void> {
        const progressOptions = {
            location: vscode.ProgressLocation.Notification,
            title: "VIKKI AI",
            cancellable: true
        };

        await vscode.window.withProgress(progressOptions, async (progress, token) => {
            try {
                progress.report({ message: "Processing selection..." });

                const fullPrompt = `${prompt}\n\nOriginal code:\n\`\`\`\n${selectedText}\n\`\`\``;
                const context = await this.getCurrentContext();
                
                const response = await this.vikkiProvider.generateCode({
                    prompt: fullPrompt,
                    context: context,
                    language: this.detectLanguage(editor.document)
                });

                if (token.isCancellationRequested) {
                    return;
                }

                progress.report({ message: "Applying changes..." });

                if (response.code) {
                    // Replace selection with generated code
                    await editor.edit(editBuilder => {
                        editBuilder.replace(editor.selection, response.code!);
                    });
                    
                    vscode.window.showInformationMessage('Code updated successfully!');
                } else {
                    vscode.window.showInformationMessage(
                        response.message || 'Code processed successfully!'
                    );
                }

            } catch (error: any) {
                if (!token.isCancellationRequested) {
                    vscode.window.showErrorMessage(`Code processing failed: ${error.message}`);
                }
            }
        });
    }

    async explainCode(selectedText: string): Promise<void> {
        try {
            const prompt = `Please explain this code in detail:\n\`\`\`\n${selectedText}\n\`\`\``;
            
            const response = await this.vikkiProvider.chat({
                message: prompt
            });

            // Show explanation in a new document
            const doc = await vscode.workspace.openTextDocument({
                content: `# Code Explanation\n\n${response.response}`,
                language: 'markdown'
            });
            
            await vscode.window.showTextDocument(doc);
            
        } catch (error: any) {
            vscode.window.showErrorMessage(`Code explanation failed: ${error.message}`);
        }
    }

    async optimizeCode(selectedText: string, editor: vscode.TextEditor): Promise<void> {
        const progressOptions = {
            location: vscode.ProgressLocation.Notification,
            title: "VIKKI AI",
            cancellable: true
        };

        await vscode.window.withProgress(progressOptions, async (progress, token) => {
            try {
                progress.report({ message: "Optimizing code..." });

                const prompt = `Please optimize this code for better performance, readability, and best practices:\n\`\`\`\n${selectedText}\n\`\`\``;
                const context = await this.getCurrentContext();
                
                const response = await this.vikkiProvider.generateCode({
                    prompt: prompt,
                    context: context,
                    language: this.detectLanguage(editor.document)
                });

                if (token.isCancellationRequested) {
                    return;
                }

                progress.report({ message: "Applying optimizations..." });

                if (response.code) {
                    // Show diff and ask for confirmation
                    const choice = await vscode.window.showInformationMessage(
                        'Code optimization complete. Apply changes?',
                        'Apply', 'Show Diff', 'Cancel'
                    );

                    if (choice === 'Apply') {
                        await editor.edit(editBuilder => {
                            editBuilder.replace(editor.selection, response.code!);
                        });
                        vscode.window.showInformationMessage('Code optimized successfully!');
                    } else if (choice === 'Show Diff') {
                        await this.showDiff(selectedText, response.code, 'Original', 'Optimized');
                    }
                }

            } catch (error: any) {
                if (!token.isCancellationRequested) {
                    vscode.window.showErrorMessage(`Code optimization failed: ${error.message}`);
                }
            }
        });
    }

    async generateTests(selectedText: string): Promise<void> {
        try {
            const prompt = `Generate comprehensive unit tests for this code:\n\`\`\`\n${selectedText}\n\`\`\``;
            const context = await this.getCurrentContext();
            
            const response = await this.vikkiProvider.generateCode({
                prompt: prompt,
                context: context,
                language: this.detectLanguage()
            });

            if (response.code) {
                // Create new test file
                const testFileName = this.generateTestFileName();
                const testUri = vscode.Uri.file(testFileName);
                
                await vscode.workspace.fs.writeFile(testUri, Buffer.from(response.code, 'utf8'));
                const doc = await vscode.workspace.openTextDocument(testUri);
                await vscode.window.showTextDocument(doc);
                
                vscode.window.showInformationMessage('Test file generated successfully!');
            }

        } catch (error: any) {
            vscode.window.showErrorMessage(`Test generation failed: ${error.message}`);
        }
    }

    async generateDocumentation(selectedText: string, editor: vscode.TextEditor): Promise<void> {
        try {
            const prompt = `Generate comprehensive documentation comments for this code:\n\`\`\`\n${selectedText}\n\`\`\``;
            const context = await this.getCurrentContext();
            
            const response = await this.vikkiProvider.generateCode({
                prompt: prompt,
                context: context,
                language: this.detectLanguage(editor.document)
            });

            if (response.code) {
                // Insert documentation above the selected code
                const line = editor.selection.start.line;
                const position = new vscode.Position(line, 0);
                
                await editor.edit(editBuilder => {
                    editBuilder.insert(position, response.code! + '\n');
                });
                
                vscode.window.showInformationMessage('Documentation generated successfully!');
            }

        } catch (error: any) {
            vscode.window.showErrorMessage(`Documentation generation failed: ${error.message}`);
        }
    }

    private async getCurrentContext(): Promise<string> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return '';
        }

        const document = editor.document;
        const workspaceFolder = vscode.workspace.getWorkspaceFolder(document.uri);
        
        let context = `File: ${document.fileName}\n`;
        context += `Language: ${document.languageId}\n`;
        
        if (workspaceFolder) {
            context += `Workspace: ${workspaceFolder.name}\n`;
        }

        // Add some surrounding context (previous and next lines)
        const currentLine = editor.selection.active.line;
        const startLine = Math.max(0, currentLine - 5);
        const endLine = Math.min(document.lineCount - 1, currentLine + 5);
        
        if (startLine < endLine) {
            const contextRange = new vscode.Range(startLine, 0, endLine, 0);
            const contextText = document.getText(contextRange);
            context += `\nSurrounding context:\n\`\`\`\n${contextText}\n\`\`\`\n`;
        }

        return context;
    }

    private detectLanguage(document?: vscode.TextDocument): string {
        if (document) {
            return document.languageId;
        }

        const editor = vscode.window.activeTextEditor;
        if (editor) {
            return editor.document.languageId;
        }

        return 'plaintext';
    }

    private async insertGeneratedCode(code: string): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        
        if (editor) {
            // Insert at cursor position
            const position = editor.selection.active;
            await editor.edit(editBuilder => {
                editBuilder.insert(position, code);
            });
        } else {
            // Create new file
            const doc = await vscode.workspace.openTextDocument({
                content: code,
                language: this.detectLanguage()
            });
            await vscode.window.showTextDocument(doc);
        }
    }

    private async handleProjectGeneration(response: any): Promise<void> {
        const choice = await vscode.window.showInformationMessage(
            `Project generated with ${response.files.length} files. What would you like to do?`,
            'Download ZIP', 'View Files', 'Open Preview'
        );

        switch (choice) {
            case 'Download ZIP':
                if (response.download_url) {
                    vscode.env.openExternal(vscode.Uri.parse(`https://vikki-ai.com${response.download_url}`));
                }
                break;
            case 'View Files':
                vscode.window.showInformationMessage(`Files: ${response.files.join(', ')}`);
                break;
            case 'Open Preview':
                if (response.preview_url) {
                    vscode.env.openExternal(vscode.Uri.parse(`https://vikki-ai.com${response.preview_url}`));
                }
                break;
        }
    }

    private async showDiff(original: string, modified: string, originalTitle: string, modifiedTitle: string): Promise<void> {
        const originalUri = vscode.Uri.parse(`untitled:${originalTitle}`);
        const modifiedUri = vscode.Uri.parse(`untitled:${modifiedTitle}`);
        
        await vscode.workspace.openTextDocument(originalUri).then(doc => {
            return vscode.window.showTextDocument(doc);
        });
        
        await vscode.workspace.openTextDocument(modifiedUri).then(doc => {
            return vscode.window.showTextDocument(doc);
        });
        
        vscode.commands.executeCommand('vscode.diff', originalUri, modifiedUri, `${originalTitle} ↔ ${modifiedTitle}`);
    }

    private generateTestFileName(): string {
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const currentFile = editor.document.fileName;
            const ext = path.extname(currentFile);
            const baseName = path.basename(currentFile, ext);
            const dir = path.dirname(currentFile);
            return path.join(dir, `${baseName}.test${ext}`);
        }
        
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            return path.join(workspaceFolder.uri.fsPath, 'test.js');
        }
        
        return 'test.js';
    }
}
