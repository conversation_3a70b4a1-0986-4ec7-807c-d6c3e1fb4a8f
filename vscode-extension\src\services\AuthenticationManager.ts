import * as vscode from 'vscode';

export class AuthenticationManager {
    private static readonly API_KEY_SECRET = 'vikkiAI.apiKey';
    private static readonly USER_INFO_SECRET = 'vikkiAI.userInfo';
    private onAuthChangedEmitter = new vscode.EventEmitter<boolean>();
    public readonly onAuthenticationChanged = this.onAuthChangedEmitter.event;

    constructor(private context: vscode.ExtensionContext) {}

    async authenticate(): Promise<boolean> {
        try {
            // Show input box for API key
            const apiKey = await vscode.window.showInputBox({
                prompt: 'Enter your VIKKI AI API Key',
                placeHolder: 'Get your API key from https://vikki-ai.com/profile',
                password: true,
                ignoreFocusOut: true,
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return 'API key cannot be empty';
                    }
                    if (value.length < 32) {
                        return 'API key seems too short. Please check your key.';
                    }
                    return null;
                }
            });

            if (!apiKey) {
                return false;
            }

            // Validate API key by making a test request
            const isValid = await this.validateApiKey(apiKey.trim());
            
            if (isValid) {
                // Store API key securely
                await this.context.secrets.store(AuthenticationManager.API_KEY_SECRET, apiKey.trim());
                
                // Get user info and store it
                const userInfo = await this.getUserInfo(apiKey.trim());
                if (userInfo) {
                    await this.context.secrets.store(
                        AuthenticationManager.USER_INFO_SECRET, 
                        JSON.stringify(userInfo)
                    );
                }

                vscode.window.showInformationMessage(
                    `Successfully authenticated with VIKKI AI! Welcome ${userInfo?.username || 'User'}!`
                );
                
                this.onAuthChangedEmitter.fire(true);
                return true;
            } else {
                vscode.window.showErrorMessage(
                    'Invalid API key. Please check your key and try again. Get your API key from https://vikki-ai.com/profile'
                );
                return false;
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Authentication failed: ${error}`);
            return false;
        }
    }

    async logout(): Promise<void> {
        await this.context.secrets.delete(AuthenticationManager.API_KEY_SECRET);
        await this.context.secrets.delete(AuthenticationManager.USER_INFO_SECRET);
        this.onAuthChangedEmitter.fire(false);
        vscode.window.showInformationMessage('Logged out from VIKKI AI');
    }

    async getApiKey(): Promise<string | undefined> {
        return await this.context.secrets.get(AuthenticationManager.API_KEY_SECRET);
    }

    async getUserInfo(): Promise<any | undefined> {
        const userInfoStr = await this.context.secrets.get(AuthenticationManager.USER_INFO_SECRET);
        return userInfoStr ? JSON.parse(userInfoStr) : undefined;
    }

    async isAuthenticated(): Promise<boolean> {
        const apiKey = await this.getApiKey();
        return !!apiKey;
    }

    private async validateApiKey(apiKey: string): Promise<boolean> {
        try {
            const config = vscode.workspace.getConfiguration('vikkiAI');
            const apiEndpoint = config.get<string>('apiEndpoint', 'https://vikki-ai.com/api');
            
            const axios = require('axios');
            const response = await axios.get(`${apiEndpoint}/users/me`, {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            return response.status === 200;
        } catch (error: any) {
            console.error('API key validation failed:', error);
            if (error.response?.status === 401) {
                return false; // Invalid API key
            }
            // For other errors (network, etc.), we'll assume the key might be valid
            // but show a warning
            if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
                const choice = await vscode.window.showWarningMessage(
                    'Cannot connect to VIKKI AI servers. Save API key anyway?',
                    'Yes', 'No'
                );
                return choice === 'Yes';
            }
            return false;
        }
    }

    private async getUserInfo(apiKey: string): Promise<any | undefined> {
        try {
            const config = vscode.workspace.getConfiguration('vikkiAI');
            const apiEndpoint = config.get<string>('apiEndpoint', 'https://vikki-ai.com/api');
            
            const axios = require('axios');
            const response = await axios.get(`${apiEndpoint}/users/me`, {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            return response.data;
        } catch (error) {
            console.error('Failed to get user info:', error);
            return undefined;
        }
    }

    async showStatus(): Promise<void> {
        const isAuth = await this.isAuthenticated();
        
        if (!isAuth) {
            const choice = await vscode.window.showInformationMessage(
                'Not connected to VIKKI AI',
                'Authenticate',
                'Get API Key'
            );
            
            if (choice === 'Authenticate') {
                await this.authenticate();
            } else if (choice === 'Get API Key') {
                vscode.env.openExternal(vscode.Uri.parse('https://vikki-ai.com/profile'));
            }
            return;
        }

        const userInfo = await this.getUserInfo();
        const apiKey = await this.getApiKey();
        
        const statusMessage = [
            `✅ Connected to VIKKI AI`,
            `👤 User: ${userInfo?.username || 'Unknown'}`,
            `📧 Email: ${userInfo?.email || 'Unknown'}`,
            `🔑 API Key: ${apiKey ? `${apiKey.substring(0, 8)}...` : 'Not set'}`,
            `💰 Plan: ${userInfo?.plan || 'Unknown'}`,
            `🎯 Tokens: ${userInfo?.tokens || 'Unknown'}`
        ].join('\n');

        const choice = await vscode.window.showInformationMessage(
            statusMessage,
            'Refresh',
            'Logout',
            'Open Dashboard'
        );

        switch (choice) {
            case 'Refresh':
                // Refresh user info
                const apiKeyForRefresh = await this.getApiKey();
                if (apiKeyForRefresh) {
                    const newUserInfo = await this.getUserInfo();
                    if (newUserInfo) {
                        await this.context.secrets.store(
                            AuthenticationManager.USER_INFO_SECRET,
                            JSON.stringify(newUserInfo)
                        );
                    }
                }
                await this.showStatus();
                break;
            case 'Logout':
                await this.logout();
                break;
            case 'Open Dashboard':
                vscode.env.openExternal(vscode.Uri.parse('https://vikki-ai.com/dashboard'));
                break;
        }
    }
}
