import React from 'react';
import { Fi<PERSON>hield, <PERSON>Lock, FiEye, FiDatabase } from 'react-icons/fi';
import PageContainer from '../components/layout/PageContainer';

const Privacy = () => {
  const sections = [
    {
      icon: <FiDatabase className="text-cyan-400" />,
      title: "Information We Collect",
      content: [
        "Account information (email, username, profile details)",
        "Generated code and project data",
        "Usage analytics and performance metrics",
        "API usage logs and token consumption",
        "Device and browser information for optimization"
      ]
    },
    {
      icon: <FiLock className="text-green-400" />,
      title: "How We Use Your Information",
      content: [
        "Provide and improve our AI code generation services",
        "Authenticate and secure your account",
        "Monitor usage and prevent abuse",
        "Send important service updates and notifications",
        "Analyze usage patterns to enhance our AI models"
      ]
    },
    {
      icon: <FiShield className="text-blue-400" />,
      title: "Data Protection",
      content: [
        "All data is encrypted in transit and at rest",
        "We use industry-standard security measures",
        "Regular security audits and penetration testing",
        "Limited access to personal data on a need-to-know basis",
        "Secure data centers with 24/7 monitoring"
      ]
    },
    {
      icon: <FiEye className="text-purple-400" />,
      title: "Your Rights",
      content: [
        "Access and download your personal data",
        "Request correction of inaccurate information",
        "Delete your account and associated data",
        "Opt-out of non-essential communications",
        "Data portability to other services"
      ]
    }
  ];

  return (
    <PageContainer>
      <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a1a2e] to-[#16213e] text-white pt-20">
        {/* Hero Section */}
        <div className="relative py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-3xl md:text-5xl lg:text-6xl font-black mb-8 tracking-tight leading-tight text-[#bbc0c0]">
              Privacy Policy
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed">
              Your privacy is important to us. Learn how we collect, use, and protect your information.
            </p>
            <p className="text-lg text-gray-400 mt-4">
              Last updated: December 2024
            </p>
          </div>
        </div>

        {/* Privacy Sections */}
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {sections.map((section, index) => (
                <div key={index} className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="text-3xl">
                      {section.icon}
                    </div>
                    <h2 className="text-2xl font-bold text-white">
                      {section.title}
                    </h2>
                  </div>
                  <ul className="space-y-3">
                    {section.content.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-300 leading-relaxed">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Detailed Policy */}
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 md:p-12">
              <h2 className="text-3xl font-bold text-white mb-8">Detailed Privacy Information</h2>
              
              <div className="space-y-8">
                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">Code Ownership</h3>
                  <p className="text-gray-300 leading-relaxed">
                    All code generated through VIKKI AI belongs to you. We do not claim ownership of your generated projects, 
                    and you are free to use them for any commercial or personal purposes. We may analyze usage patterns 
                    to improve our AI models, but your specific code remains private.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">Data Retention</h3>
                  <p className="text-gray-300 leading-relaxed">
                    We retain your account information and generated projects for as long as your account is active. 
                    You can delete individual projects or your entire account at any time. Deleted data is permanently 
                    removed from our systems within 30 days.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">Third-Party Services</h3>
                  <p className="text-gray-300 leading-relaxed">
                    We use trusted third-party services for authentication, analytics, and infrastructure. These services 
                    are bound by strict data processing agreements and only receive the minimum data necessary to provide 
                    their services.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">International Transfers</h3>
                  <p className="text-gray-300 leading-relaxed">
                    Your data may be processed in countries other than your own. We ensure that all international 
                    transfers comply with applicable data protection laws and use appropriate safeguards to protect 
                    your information.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">Children's Privacy</h3>
                  <p className="text-gray-300 leading-relaxed">
                    VIKKI AI is not intended for children under 13. We do not knowingly collect personal information 
                    from children under 13. If you believe we have collected such information, please contact us 
                    immediately.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">Changes to This Policy</h3>
                  <p className="text-gray-300 leading-relaxed">
                    We may update this privacy policy from time to time. We will notify you of any material changes 
                    by email or through our service. Your continued use of VIKKI AI after such changes constitutes 
                    acceptance of the updated policy.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-cyan-900/20 to-blue-900/20 backdrop-blur-sm border border-cyan-500/20 rounded-3xl p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Questions About Privacy?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                If you have any questions about this privacy policy or how we handle your data, 
                please don't hesitate to contact us.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105">
                  Contact Privacy Team
                </button>
                <button className="border border-gray-600 hover:border-cyan-400 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:bg-cyan-400/10">
                  Download Your Data
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default Privacy;
