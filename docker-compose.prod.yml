# Production override for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: '3.8'

services:
  mongodb:
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD:-secure_password_here}
    volumes:
      - mongodb_data:/data/db
      - ./backups:/backups  # Backup directory
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  backend:
    environment:
      - MONGODB_URI=mongodb://${MONGO_INITDB_ROOT_USERNAME:-admin}:${MONGO_INITDB_ROOT_PASSWORD:-secure_password_here}@mongodb:27017/vikki-ai?authSource=admin
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - RAZORPAY_KEY_ID=${RAZORPAY_KEY_ID}
      - RAZORPAY_KEY_SECRET=${RAZORPAY_KEY_SECRET}
      - RAZORPAY_WEBHOOK_SECRET=${RAZORPAY_WEBHOOK_SECRET}
      - STABILITY_API_KEY=${STABILITY_API_KEY}
      - PAYMENT_SUCCESS_URL=${FRONTEND_URL}/payment/success
      - PAYMENT_CANCEL_URL=${FRONTEND_URL}/payment/cancel
      - ENVIRONMENT=production
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
      replicas: 2  # Scale for high availability
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"

  frontend:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Add Redis for session storage and caching
  redis:
    image: redis:7-alpine
    container_name: vikki-ai-redis
    restart: always
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password_here}
    volumes:
      - redis_data:/data
    networks:
      - vikki-ai-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Add Nginx load balancer (if scaling backend)
  nginx-lb:
    image: nginx:alpine
    container_name: vikki-ai-nginx-lb
    restart: always
    ports:
      - "8080:80"  # Load balancer for backend
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
    networks:
      - vikki-ai-network
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'

volumes:
  redis_data:
    driver: local

# Production network with custom subnet
networks:
  vikki-ai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
