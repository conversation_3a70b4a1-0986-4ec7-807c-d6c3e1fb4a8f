#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Building VIKKI AI VS Code Extension...\n');

// Check if we're in the right directory
const packageJsonPath = path.join(__dirname, '..', 'package.json');
if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ Error: package.json not found. Make sure you\'re in the extension directory.');
    process.exit(1);
}

const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
console.log(`📦 Building ${packageJson.displayName} v${packageJson.version}`);

try {
    // Clean previous build
    console.log('\n🧹 Cleaning previous build...');
    if (fs.existsSync(path.join(__dirname, '..', 'out'))) {
        execSync('rm -rf out', { cwd: path.join(__dirname, '..'), stdio: 'inherit' });
    }

    // Install dependencies
    console.log('\n📥 Installing dependencies...');
    execSync('npm install', { cwd: path.join(__dirname, '..'), stdio: 'inherit' });

    // Compile TypeScript
    console.log('\n🔨 Compiling TypeScript...');
    execSync('npm run compile', { cwd: path.join(__dirname, '..'), stdio: 'inherit' });

    // Run linting
    console.log('\n🔍 Running linter...');
    try {
        execSync('npm run lint', { cwd: path.join(__dirname, '..'), stdio: 'inherit' });
        console.log('✅ Linting passed');
    } catch (error) {
        console.log('⚠️  Linting warnings found (continuing build)');
    }

    // Create icon from SVG if needed
    console.log('\n🎨 Processing assets...');
    const iconSvgPath = path.join(__dirname, '..', 'assets', 'icon.svg');
    const iconPngPath = path.join(__dirname, '..', 'assets', 'icon.png');
    
    if (fs.existsSync(iconSvgPath) && !fs.existsSync(iconPngPath)) {
        console.log('Converting SVG icon to PNG...');
        // Note: This would require a tool like sharp or imagemagick
        // For now, we'll just copy the SVG and note that PNG is preferred
        console.log('⚠️  Note: PNG icon recommended for better compatibility');
    }

    // Validate package.json
    console.log('\n✅ Validating package.json...');
    const requiredFields = ['name', 'displayName', 'description', 'version', 'publisher', 'engines', 'main'];
    const missingFields = requiredFields.filter(field => !packageJson[field]);
    
    if (missingFields.length > 0) {
        console.error(`❌ Missing required fields in package.json: ${missingFields.join(', ')}`);
        process.exit(1);
    }

    // Check if main file exists
    const mainFile = path.join(__dirname, '..', packageJson.main);
    if (!fs.existsSync(mainFile)) {
        console.error(`❌ Main file not found: ${packageJson.main}`);
        process.exit(1);
    }

    console.log('✅ Package validation passed');

    // Package extension
    console.log('\n📦 Packaging extension...');
    try {
        execSync('npm run package', { cwd: path.join(__dirname, '..'), stdio: 'inherit' });
        console.log('✅ Extension packaged successfully');
    } catch (error) {
        console.log('⚠️  Packaging failed, but build is complete');
        console.log('💡 Run "npm install -g @vscode/vsce" to enable packaging');
    }

    // Build summary
    console.log('\n🎉 Build completed successfully!');
    console.log('\n📋 Build Summary:');
    console.log(`   Extension: ${packageJson.displayName}`);
    console.log(`   Version: ${packageJson.version}`);
    console.log(`   Publisher: ${packageJson.publisher}`);
    console.log(`   Main: ${packageJson.main}`);
    
    // Check output directory
    const outDir = path.join(__dirname, '..', 'out');
    if (fs.existsSync(outDir)) {
        const files = fs.readdirSync(outDir);
        console.log(`   Output files: ${files.length} files in /out`);
    }

    // Check for VSIX file
    const vsixFiles = fs.readdirSync(path.join(__dirname, '..')).filter(f => f.endsWith('.vsix'));
    if (vsixFiles.length > 0) {
        console.log(`   Package: ${vsixFiles[0]}`);
    }

    console.log('\n🚀 Ready for installation or publishing!');
    console.log('\n💡 Next steps:');
    console.log('   • Test: Press F5 in VS Code to launch Extension Development Host');
    console.log('   • Install: code --install-extension *.vsix');
    console.log('   • Publish: vsce publish');

} catch (error) {
    console.error('\n❌ Build failed:', error.message);
    process.exit(1);
}
