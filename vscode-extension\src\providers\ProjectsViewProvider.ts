import * as vscode from 'vscode';
import { VikkiAIProvider } from './VikkiAIProvider';

export class ProjectsViewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'vikkiAI.projectsView';
    private _view?: vscode.WebviewView;

    constructor(
        private readonly _context: vscode.ExtensionContext,
        private readonly _vikkiProvider: VikkiAIProvider
    ) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._context.extensionUri
            ]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(
            async (data) => {
                switch (data.type) {
                    case 'loadProjects':
                        await this.loadProjects();
                        break;
                    case 'downloadProject':
                        await this.downloadProject(data.projectId);
                        break;
                    case 'openProject':
                        await this.openProject(data.projectId);
                        break;
                    case 'deleteProject':
                        await this.deleteProject(data.projectId);
                        break;
                    case 'refreshProjects':
                        await this.loadProjects();
                        break;
                }
            },
            undefined,
            this._context.subscriptions
        );

        // Load projects on initialization
        this.loadProjects();
    }

    private async loadProjects() {
        if (!this._view) {
            return;
        }

        try {
            this._view.webview.postMessage({
                type: 'setLoading',
                loading: true
            });

            const projects = await this._vikkiProvider.getUserProjects();
            
            this._view.webview.postMessage({
                type: 'setLoading',
                loading: false
            });

            this._view.webview.postMessage({
                type: 'updateProjects',
                projects: projects.projects || []
            });

        } catch (error: any) {
            this._view.webview.postMessage({
                type: 'setLoading',
                loading: false
            });

            this._view.webview.postMessage({
                type: 'showError',
                message: `Failed to load projects: ${error.message}`
            });
        }
    }

    private async downloadProject(projectId: string) {
        try {
            const projectData = await this._vikkiProvider.downloadProject(projectId);
            
            // Save the ZIP file
            const saveUri = await vscode.window.showSaveDialog({
                defaultUri: vscode.Uri.file(`vikki-ai-project-${projectId}.zip`),
                filters: {
                    'ZIP files': ['zip']
                }
            });

            if (saveUri) {
                await vscode.workspace.fs.writeFile(saveUri, projectData);
                vscode.window.showInformationMessage(`Project downloaded to ${saveUri.fsPath}`);
            }

        } catch (error: any) {
            vscode.window.showErrorMessage(`Failed to download project: ${error.message}`);
        }
    }

    private async openProject(projectId: string) {
        // Open project preview in browser
        const previewUrl = `https://vikki-ai.com/projects/${projectId}/preview`;
        vscode.env.openExternal(vscode.Uri.parse(previewUrl));
    }

    private async deleteProject(projectId: string) {
        const choice = await vscode.window.showWarningMessage(
            'Are you sure you want to delete this project?',
            'Delete', 'Cancel'
        );

        if (choice === 'Delete') {
            try {
                // Note: This would require a delete endpoint in the API
                // For now, we'll just show a message
                vscode.window.showInformationMessage('Project deletion is not yet implemented in the API');
                
                // Refresh the projects list
                await this.loadProjects();

            } catch (error: any) {
                vscode.window.showErrorMessage(`Failed to delete project: ${error.message}`);
            }
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>VIKKI AI Projects</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    margin: 0;
                    padding: 10px;
                }
                
                .header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 15px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid var(--vscode-panel-border);
                }
                
                .header h3 {
                    margin: 0;
                    color: var(--vscode-button-background);
                }
                
                .refresh-btn {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    cursor: pointer;
                    font-size: 0.9em;
                }
                
                .refresh-btn:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                
                .loading {
                    text-align: center;
                    padding: 20px;
                    display: none;
                }
                
                .loading.show {
                    display: block;
                }
                
                .spinner {
                    border: 2px solid var(--vscode-panel-border);
                    border-top: 2px solid var(--vscode-button-background);
                    border-radius: 50%;
                    width: 20px;
                    height: 20px;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 10px;
                }
                
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                
                .projects-container {
                    max-height: 400px;
                    overflow-y: auto;
                }
                
                .project-item {
                    background-color: var(--vscode-editor-selectionBackground);
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 6px;
                    padding: 12px;
                    margin-bottom: 10px;
                    transition: background-color 0.2s;
                }
                
                .project-item:hover {
                    background-color: var(--vscode-list-hoverBackground);
                }
                
                .project-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 8px;
                }
                
                .project-name {
                    font-weight: 600;
                    color: var(--vscode-button-background);
                    margin: 0;
                    font-size: 1em;
                }
                
                .project-date {
                    font-size: 0.8em;
                    opacity: 0.7;
                }
                
                .project-description {
                    font-size: 0.9em;
                    margin: 8px 0;
                    opacity: 0.9;
                    line-height: 1.4;
                }
                
                .project-meta {
                    display: flex;
                    gap: 15px;
                    font-size: 0.8em;
                    opacity: 0.7;
                    margin-bottom: 10px;
                }
                
                .project-actions {
                    display: flex;
                    gap: 8px;
                    flex-wrap: wrap;
                }
                
                .action-btn {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 3px;
                    padding: 4px 8px;
                    cursor: pointer;
                    font-size: 0.8em;
                    transition: background-color 0.2s;
                }
                
                .action-btn:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                
                .action-btn.secondary {
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                }
                
                .action-btn.secondary:hover {
                    background-color: var(--vscode-button-secondaryHoverBackground);
                }
                
                .action-btn.danger {
                    background-color: var(--vscode-inputValidation-errorBackground);
                    color: var(--vscode-inputValidation-errorForeground);
                }
                
                .empty-state {
                    text-align: center;
                    padding: 40px 20px;
                    opacity: 0.7;
                }
                
                .empty-state h4 {
                    margin: 0 0 10px 0;
                    color: var(--vscode-button-background);
                }
                
                .empty-state p {
                    margin: 0;
                    font-size: 0.9em;
                }
                
                .error-message {
                    background-color: var(--vscode-inputValidation-errorBackground);
                    border: 1px solid var(--vscode-inputValidation-errorBorder);
                    border-radius: 4px;
                    padding: 10px;
                    margin-bottom: 15px;
                    font-size: 0.9em;
                }
                
                .stats {
                    background-color: var(--vscode-editor-selectionBackground);
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 6px;
                    padding: 10px;
                    margin-bottom: 15px;
                    font-size: 0.9em;
                }
                
                .stats-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                }
                
                .stat-item {
                    text-align: center;
                }
                
                .stat-value {
                    font-weight: 600;
                    color: var(--vscode-button-background);
                    font-size: 1.2em;
                }
                
                .stat-label {
                    opacity: 0.7;
                    font-size: 0.8em;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h3>📁 Generated Projects</h3>
                <button id="refreshBtn" class="refresh-btn">🔄 Refresh</button>
            </div>
            
            <div class="stats" id="statsContainer" style="display: none;">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="totalProjects">0</div>
                        <div class="stat-label">Total Projects</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="recentProjects">0</div>
                        <div class="stat-label">This Month</div>
                    </div>
                </div>
            </div>
            
            <div class="loading" id="loadingIndicator">
                <div class="spinner"></div>
                <p>Loading projects...</p>
            </div>
            
            <div id="errorContainer"></div>
            
            <div class="projects-container" id="projectsContainer">
                <div class="empty-state">
                    <h4>No projects yet</h4>
                    <p>Generate your first project using VIKKI AI!</p>
                </div>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                const refreshBtn = document.getElementById('refreshBtn');
                const loadingIndicator = document.getElementById('loadingIndicator');
                const projectsContainer = document.getElementById('projectsContainer');
                const errorContainer = document.getElementById('errorContainer');
                const statsContainer = document.getElementById('statsContainer');
                const totalProjectsEl = document.getElementById('totalProjects');
                const recentProjectsEl = document.getElementById('recentProjects');

                function setLoading(loading) {
                    if (loading) {
                        loadingIndicator.classList.add('show');
                        refreshBtn.disabled = true;
                    } else {
                        loadingIndicator.classList.remove('show');
                        refreshBtn.disabled = false;
                    }
                }

                function showError(message) {
                    errorContainer.innerHTML = \`
                        <div class="error-message">
                            ❌ \${message}
                        </div>
                    \`;
                }

                function clearError() {
                    errorContainer.innerHTML = '';
                }

                function updateProjects(projects) {
                    clearError();
                    
                    if (!projects || projects.length === 0) {
                        projectsContainer.innerHTML = \`
                            <div class="empty-state">
                                <h4>No projects yet</h4>
                                <p>Generate your first project using VIKKI AI!</p>
                            </div>
                        \`;
                        statsContainer.style.display = 'none';
                        return;
                    }

                    // Update stats
                    const now = new Date();
                    const thisMonth = projects.filter(p => {
                        const projectDate = new Date(p.created_at);
                        return projectDate.getMonth() === now.getMonth() && 
                               projectDate.getFullYear() === now.getFullYear();
                    }).length;

                    totalProjectsEl.textContent = projects.length;
                    recentProjectsEl.textContent = thisMonth;
                    statsContainer.style.display = 'block';

                    // Render projects
                    const projectsHtml = projects.map(project => \`
                        <div class="project-item">
                            <div class="project-header">
                                <h4 class="project-name">\${project.name || 'Untitled Project'}</h4>
                                <div class="project-date">\${formatDate(project.created_at)}</div>
                            </div>
                            
                            <div class="project-description">
                                \${project.description || 'No description available'}
                            </div>
                            
                            <div class="project-meta">
                                <span>📄 \${project.files?.length || 0} files</span>
                                <span>🏷️ \${project.type || 'Unknown'}</span>
                            </div>
                            
                            <div class="project-actions">
                                <button class="action-btn" onclick="openProject('\${project.id}')">
                                    👁️ Preview
                                </button>
                                <button class="action-btn secondary" onclick="downloadProject('\${project.id}')">
                                    💾 Download
                                </button>
                                <button class="action-btn danger" onclick="deleteProject('\${project.id}')">
                                    🗑️ Delete
                                </button>
                            </div>
                        </div>
                    \`).join('');

                    projectsContainer.innerHTML = projectsHtml;
                }

                function formatDate(dateString) {
                    const date = new Date(dateString);
                    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                }

                function openProject(projectId) {
                    vscode.postMessage({
                        type: 'openProject',
                        projectId: projectId
                    });
                }

                function downloadProject(projectId) {
                    vscode.postMessage({
                        type: 'downloadProject',
                        projectId: projectId
                    });
                }

                function deleteProject(projectId) {
                    vscode.postMessage({
                        type: 'deleteProject',
                        projectId: projectId
                    });
                }

                function refreshProjects() {
                    vscode.postMessage({
                        type: 'refreshProjects'
                    });
                }

                // Event listeners
                refreshBtn.addEventListener('click', refreshProjects);

                // Handle messages from extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    switch (message.type) {
                        case 'setLoading':
                            setLoading(message.loading);
                            break;
                        case 'updateProjects':
                            updateProjects(message.projects);
                            break;
                        case 'showError':
                            showError(message.message);
                            break;
                    }
                });

                // Load projects on startup
                vscode.postMessage({
                    type: 'loadProjects'
                });
            </script>
        </body>
        </html>`;
    }
}
