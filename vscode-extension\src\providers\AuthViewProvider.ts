import * as vscode from 'vscode';
import { AuthenticationManager } from '../services/AuthenticationManager';

export class AuthViewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'vikkiAI.authView';
    private _view?: vscode.WebviewView;

    constructor(
        private readonly _context: vscode.ExtensionContext,
        private readonly _authManager: AuthenticationManager
    ) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._context.extensionUri
            ]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(
            async (data) => {
                switch (data.type) {
                    case 'authenticate':
                        await this.handleAuthentication(data.apiKey);
                        break;
                    case 'openWebsite':
                        vscode.env.openExternal(vscode.Uri.parse('https://vikki-ai.com'));
                        break;
                    case 'getApiKey':
                        vscode.env.openExternal(vscode.Uri.parse('https://vikki-ai.com/profile'));
                        break;
                    case 'openDocs':
                        vscode.env.openExternal(vscode.Uri.parse('https://vikki-ai.com/docs'));
                        break;
                }
            },
            undefined,
            this._context.subscriptions
        );
    }

    private async handleAuthentication(apiKey: string) {
        if (!this._view) {
            return;
        }

        try {
            // Show loading state
            this._view.webview.postMessage({
                type: 'setLoading',
                loading: true
            });

            // Validate API key (simplified version)
            if (!apiKey || apiKey.trim().length < 32) {
                throw new Error('Invalid API key format');
            }

            // Store the API key using AuthenticationManager
            await this._context.secrets.store('vikkiAI.apiKey', apiKey.trim());

            // Show success message
            this._view.webview.postMessage({
                type: 'setLoading',
                loading: false
            });

            this._view.webview.postMessage({
                type: 'showMessage',
                message: 'Successfully authenticated with VIKKI AI!',
                type: 'success'
            });

            // Trigger authentication changed event
            vscode.commands.executeCommand('setContext', 'vikkiAI.authenticated', true);

            // Show success notification
            vscode.window.showInformationMessage('Successfully connected to VIKKI AI!');

        } catch (error: any) {
            this._view.webview.postMessage({
                type: 'setLoading',
                loading: false
            });

            this._view.webview.postMessage({
                type: 'showMessage',
                message: `Authentication failed: ${error.message}`,
                messageType: 'error'
            });
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>VIKKI AI Authentication</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    margin: 0;
                    padding: 20px;
                    line-height: 1.6;
                }
                
                .container {
                    max-width: 400px;
                    margin: 0 auto;
                }
                
                .logo {
                    text-align: center;
                    margin-bottom: 30px;
                }
                
                .logo h1 {
                    color: var(--vscode-button-background);
                    margin: 0;
                    font-size: 2em;
                }
                
                .logo p {
                    margin: 5px 0 0 0;
                    opacity: 0.8;
                    font-size: 0.9em;
                }
                
                .auth-form {
                    background-color: var(--vscode-editor-selectionBackground);
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 8px;
                    padding: 20px;
                    margin-bottom: 20px;
                }
                
                .form-group {
                    margin-bottom: 15px;
                }
                
                label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: 600;
                }
                
                .api-key-input {
                    width: 100%;
                    padding: 10px;
                    border: 1px solid var(--vscode-input-border);
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border-radius: 4px;
                    font-family: monospace;
                    font-size: 0.9em;
                    box-sizing: border-box;
                }
                
                .api-key-input:focus {
                    outline: none;
                    border-color: var(--vscode-button-background);
                }
                
                .button {
                    width: 100%;
                    padding: 12px;
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-family: inherit;
                    font-size: inherit;
                    font-weight: 600;
                    margin-bottom: 10px;
                    transition: background-color 0.2s;
                }
                
                .button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                
                .button:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
                
                .button.secondary {
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                }
                
                .button.secondary:hover {
                    background-color: var(--vscode-button-secondaryHoverBackground);
                }
                
                .help-text {
                    font-size: 0.85em;
                    opacity: 0.8;
                    margin-top: 5px;
                }
                
                .links {
                    text-align: center;
                    margin-top: 20px;
                }
                
                .links a {
                    color: var(--vscode-textLink-foreground);
                    text-decoration: none;
                    margin: 0 10px;
                    font-size: 0.9em;
                }
                
                .links a:hover {
                    text-decoration: underline;
                }
                
                .message {
                    padding: 10px;
                    border-radius: 4px;
                    margin-bottom: 15px;
                    font-size: 0.9em;
                }
                
                .message.success {
                    background-color: var(--vscode-inputValidation-infoBackground);
                    border-left: 3px solid var(--vscode-button-background);
                }
                
                .message.error {
                    background-color: var(--vscode-inputValidation-errorBackground);
                    border-left: 3px solid var(--vscode-inputValidation-errorBorder);
                }
                
                .loading {
                    display: none;
                    text-align: center;
                    padding: 20px;
                }
                
                .loading.show {
                    display: block;
                }
                
                .spinner {
                    border: 2px solid var(--vscode-panel-border);
                    border-top: 2px solid var(--vscode-button-background);
                    border-radius: 50%;
                    width: 20px;
                    height: 20px;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 10px;
                }
                
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                
                .features {
                    background-color: var(--vscode-editor-selectionBackground);
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 8px;
                    padding: 20px;
                    margin-top: 20px;
                }
                
                .features h3 {
                    margin-top: 0;
                    color: var(--vscode-button-background);
                }
                
                .features ul {
                    margin: 0;
                    padding-left: 20px;
                }
                
                .features li {
                    margin-bottom: 8px;
                    font-size: 0.9em;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="logo">
                    <h1>🤖 VIKKI AI</h1>
                    <p>AI-Powered Code Generation & Analysis</p>
                </div>
                
                <div class="auth-form">
                    <div id="messageContainer"></div>
                    
                    <div class="loading" id="loadingIndicator">
                        <div class="spinner"></div>
                        <p>Authenticating...</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="apiKeyInput">API Key</label>
                        <input 
                            type="password" 
                            id="apiKeyInput" 
                            class="api-key-input" 
                            placeholder="Enter your VIKKI AI API key"
                        />
                        <div class="help-text">
                            Get your API key from your VIKKI AI profile page
                        </div>
                    </div>
                    
                    <button id="authenticateBtn" class="button">
                        Connect to VIKKI AI
                    </button>
                    
                    <button id="getApiKeyBtn" class="button secondary">
                        Get API Key
                    </button>
                </div>
                
                <div class="features">
                    <h3>✨ Features</h3>
                    <ul>
                        <li>🎯 Intelligent code generation</li>
                        <li>🔍 Project analysis & insights</li>
                        <li>💬 Interactive AI chat</li>
                        <li>🧪 Automated test generation</li>
                        <li>📚 Documentation generation</li>
                        <li>⚡ Code optimization</li>
                    </ul>
                </div>
                
                <div class="links">
                    <a href="#" id="websiteLink">Visit Website</a>
                    <a href="#" id="docsLink">Documentation</a>
                </div>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                const apiKeyInput = document.getElementById('apiKeyInput');
                const authenticateBtn = document.getElementById('authenticateBtn');
                const getApiKeyBtn = document.getElementById('getApiKeyBtn');
                const loadingIndicator = document.getElementById('loadingIndicator');
                const messageContainer = document.getElementById('messageContainer');
                const websiteLink = document.getElementById('websiteLink');
                const docsLink = document.getElementById('docsLink');

                function authenticate() {
                    const apiKey = apiKeyInput.value.trim();
                    if (!apiKey) {
                        showMessage('Please enter your API key', 'error');
                        return;
                    }
                    
                    vscode.postMessage({
                        type: 'authenticate',
                        apiKey: apiKey
                    });
                }

                function showMessage(message, type = 'info') {
                    messageContainer.innerHTML = \`
                        <div class="message \${type}">
                            \${message}
                        </div>
                    \`;
                }

                function setLoading(loading) {
                    if (loading) {
                        loadingIndicator.classList.add('show');
                        authenticateBtn.disabled = true;
                        authenticateBtn.textContent = 'Connecting...';
                    } else {
                        loadingIndicator.classList.remove('show');
                        authenticateBtn.disabled = false;
                        authenticateBtn.textContent = 'Connect to VIKKI AI';
                    }
                }

                // Event listeners
                authenticateBtn.addEventListener('click', authenticate);
                
                getApiKeyBtn.addEventListener('click', () => {
                    vscode.postMessage({ type: 'getApiKey' });
                });
                
                websiteLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    vscode.postMessage({ type: 'openWebsite' });
                });
                
                docsLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    vscode.postMessage({ type: 'openDocs' });
                });
                
                apiKeyInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        authenticate();
                    }
                });

                // Handle messages from extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    switch (message.type) {
                        case 'setLoading':
                            setLoading(message.loading);
                            break;
                        case 'showMessage':
                            showMessage(message.message, message.messageType);
                            break;
                    }
                });
            </script>
        </body>
        </html>`;
    }
}
