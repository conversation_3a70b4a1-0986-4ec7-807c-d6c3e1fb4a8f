#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Setting up VIKKI AI VS Code Extension for development...\n');

try {
    // Check Node.js version
    const nodeVersion = process.version;
    console.log(`📋 Node.js version: ${nodeVersion}`);
    
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 16) {
        console.error('❌ Node.js 16 or higher is required');
        process.exit(1);
    }

    // Install dependencies
    console.log('\n📥 Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });

    // Install development tools globally if not present
    console.log('\n🛠️  Checking development tools...');
    
    try {
        execSync('vsce --version', { stdio: 'pipe' });
        console.log('✅ vsce (VS Code Extension Manager) is installed');
    } catch (error) {
        console.log('📦 Installing vsce...');
        execSync('npm install -g @vscode/vsce', { stdio: 'inherit' });
    }

    try {
        execSync('typescript --version', { stdio: 'pipe' });
        console.log('✅ TypeScript is available');
    } catch (error) {
        console.log('📦 Installing TypeScript...');
        execSync('npm install -g typescript', { stdio: 'inherit' });
    }

    // Create launch configuration for debugging
    console.log('\n🔧 Setting up VS Code configuration...');
    
    const vscodeDir = path.join(__dirname, '..', '.vscode');
    if (!fs.existsSync(vscodeDir)) {
        fs.mkdirSync(vscodeDir);
    }

    const launchConfig = {
        "version": "0.2.0",
        "configurations": [
            {
                "name": "Run Extension",
                "type": "extensionHost",
                "request": "launch",
                "args": [
                    "--extensionDevelopmentPath=${workspaceFolder}"
                ],
                "outFiles": [
                    "${workspaceFolder}/out/**/*.js"
                ],
                "preLaunchTask": "${workspaceFolder}/npm: watch"
            },
            {
                "name": "Extension Tests",
                "type": "extensionHost",
                "request": "launch",
                "args": [
                    "--extensionDevelopmentPath=${workspaceFolder}",
                    "--extensionTestsPath=${workspaceFolder}/out/test/suite/index"
                ],
                "outFiles": [
                    "${workspaceFolder}/out/test/**/*.js"
                ],
                "preLaunchTask": "${workspaceFolder}/npm: watch"
            }
        ]
    };

    const launchPath = path.join(vscodeDir, 'launch.json');
    fs.writeFileSync(launchPath, JSON.stringify(launchConfig, null, 2));
    console.log('✅ Created .vscode/launch.json');

    const tasksConfig = {
        "version": "2.0.0",
        "tasks": [
            {
                "type": "npm",
                "script": "watch",
                "problemMatcher": "$tsc-watch",
                "isBackground": true,
                "presentation": {
                    "reveal": "never"
                },
                "group": {
                    "kind": "build",
                    "isDefault": true
                }
            }
        ]
    };

    const tasksPath = path.join(vscodeDir, 'tasks.json');
    fs.writeFileSync(tasksPath, JSON.stringify(tasksConfig, null, 2));
    console.log('✅ Created .vscode/tasks.json');

    const settingsConfig = {
        "typescript.preferences.includePackageJsonAutoImports": "on",
        "typescript.suggest.autoImports": true,
        "editor.codeActionsOnSave": {
            "source.fixAll.eslint": true
        },
        "eslint.validate": [
            "typescript"
        ]
    };

    const settingsPath = path.join(vscodeDir, 'settings.json');
    fs.writeFileSync(settingsPath, JSON.stringify(settingsConfig, null, 2));
    console.log('✅ Created .vscode/settings.json');

    // Compile TypeScript
    console.log('\n🔨 Initial TypeScript compilation...');
    execSync('npm run compile', { stdio: 'inherit' });

    // Create sample .env file
    console.log('\n📝 Creating sample configuration...');
    const envSample = `# VIKKI AI VS Code Extension Development Configuration
# Copy this file to .env and update with your values

# Development API endpoint (optional)
VIKKI_API_ENDPOINT=http://localhost:8000/api

# Test API key for development (optional)
VIKKI_TEST_API_KEY=your_test_api_key_here

# Debug mode
DEBUG=true
`;

    const envSamplePath = path.join(__dirname, '..', '.env.example');
    fs.writeFileSync(envSamplePath, envSample);
    console.log('✅ Created .env.example');

    console.log('\n🎉 Development setup completed successfully!');
    console.log('\n📋 What\'s been set up:');
    console.log('   ✅ Dependencies installed');
    console.log('   ✅ Development tools configured');
    console.log('   ✅ VS Code debugging configuration');
    console.log('   ✅ TypeScript compiled');
    console.log('   ✅ Sample configuration files');

    console.log('\n🚀 Next steps:');
    console.log('   1. Open this folder in VS Code');
    console.log('   2. Press F5 to launch the Extension Development Host');
    console.log('   3. In the new window, open Command Palette (Ctrl+Shift+P)');
    console.log('   4. Type "VIKKI AI" to see available commands');
    console.log('   5. Set your API key using "VIKKI AI: Set API Key"');

    console.log('\n🔧 Development commands:');
    console.log('   • npm run watch    - Watch for changes and recompile');
    console.log('   • npm run compile  - Compile TypeScript');
    console.log('   • npm run lint     - Run ESLint');
    console.log('   • npm run test     - Run tests');
    console.log('   • npm run package  - Package extension');

    console.log('\n📚 Resources:');
    console.log('   • VS Code Extension API: https://code.visualstudio.com/api');
    console.log('   • VIKKI AI Docs: https://vikki-ai.com/docs');
    console.log('   • Extension Guidelines: https://code.visualstudio.com/api/references/extension-guidelines');

} catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   • Make sure you have Node.js 16+ installed');
    console.error('   • Check your internet connection');
    console.error('   • Try running with administrator/sudo privileges');
    process.exit(1);
}
