import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>Copy, <PERSON>Check, FiTerminal, FiBook, FiKey } from 'react-icons/fi';
import PageContainer from '../components/layout/PageContainer';

const ApiDocs = () => {
  const [copiedCode, setCopiedCode] = useState('');

  const copyToClipboard = (code, id) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(id);
    setTimeout(() => setCopiedCode(''), 2000);
  };

  const CodeBlock = ({ code, language, id, title }) => (
    <div className="bg-gray-900/80 border border-gray-700/50 rounded-xl overflow-hidden">
      <div className="flex items-center justify-between px-4 py-3 bg-gray-800/50 border-b border-gray-700/50">
        <div className="flex items-center gap-2">
          <FiTerminal className="text-cyan-400" />
          <span className="text-sm font-medium text-gray-300">{title}</span>
          <span className="text-xs text-gray-500 bg-gray-700/50 px-2 py-1 rounded">{language}</span>
        </div>
        <button
          onClick={() => copyToClipboard(code, id)}
          className="flex items-center gap-2 text-sm text-gray-400 hover:text-cyan-400 transition-colors"
        >
          {copiedCode === id ? <FiCheck className="text-green-400" /> : <FiCopy />}
          {copiedCode === id ? 'Copied!' : 'Copy'}
        </button>
      </div>
      <pre className="p-4 overflow-x-auto text-sm">
        <code className="text-gray-300">{code}</code>
      </pre>
    </div>
  );

  const endpoints = [
    {
      method: 'POST',
      path: '/api/generate',
      description: 'Generate a complete project from a text prompt',
      tokens: 50
    },
    {
      method: 'POST',
      path: '/api/chat',
      description: 'Chat with AI for code modifications and help',
      tokens: 10
    },
    {
      method: 'GET',
      path: '/api/users/projects',
      description: 'Get list of user\'s generated projects',
      tokens: 10
    },
    {
      method: 'POST',
      path: '/api/analyze',
      description: 'Analyze existing code files',
      tokens: 50
    }
  ];

  const ideIntegrations = [
    {
      name: 'VS Code',
      icon: '🔵',
      description: 'Official VS Code extension for VIKKI AI',
    code: `// ✅ VIKKI AI VS Code extension is now available!
    // 👉 Install it from the VS Code Marketplace:
    // https://marketplace.visualstudio.com/items?itemName=vikki-ai.vikki-extension
    // Or use the API directly in your own extension:

const vikkiApi = require('vikki-ai-sdk');

const client = new vikkiApi.Client({
  apiKey: 'your-api-key-here',
  baseUrl: 'https://api.vikki-ai.com'
});

// Generate code
const result = await client.generate({
  prompt: 'Create a React component for user profile',
  language: 'javascript'
});

console.log(result.code);`
    },
    {
      name: 'IntelliJ IDEA',
      icon: '🟠',
      description: 'Plugin for JetBrains IDEs',
      code: `// Add VIKKI AI plugin to IntelliJ IDEA
// Configure API key in settings

import com.vikki.ai.VikkiClient;

VikkiClient client = new VikkiClient("your-api-key-here");

// Generate Java code
GenerateRequest request = new GenerateRequest()
    .setPrompt("Create a Spring Boot REST controller")
    .setLanguage("java");

GenerateResponse response = client.generate(request);
System.out.println(response.getCode());`
    },
    {
      name: 'Sublime Text',
      icon: '🟡',
      description: 'Package for Sublime Text editor',
      code: `# Install VIKKI AI package via Package Control
# Configure API key in package settings

import sublime
import sublime_plugin
import requests

class VikkiGenerateCommand(sublime_plugin.TextCommand):
    def run(self, edit):
        api_key = "your-api-key-here"
        prompt = "Create a Python function for data validation"
        
        response = requests.post(
            "https://api.vikki-ai.com/api/generate",
            headers={"Authorization": f"Bearer {api_key}"},
            json={"prompt": prompt}
        )
        
        result = response.json()
        self.view.insert(edit, 0, result['code'])`
    }
  ];

  return (
    <PageContainer>
      <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a1a2e] to-[#16213e] text-white pt-20">
        {/* Hero Section */}
        <div className="relative py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto text-center">
            <h1 className="text-3xl md:text-5xl lg:text-6xl font-black mb-8 tracking-tight leading-tight text-[#bbc0c0]">
              API Documentation
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed max-w-4xl mx-auto">
              Integrate VIKKI AI into your favorite IDE and development workflow with our powerful REST API
            </p>
          </div>
        </div>

        {/* Quick Start */}
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center gap-4 mb-8">
              <FiKey className="text-3xl text-cyan-400" />
              <h2 className="text-3xl md:text-4xl font-bold text-white">Quick Start</h2>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <div>
                <h3 className="text-2xl font-bold text-white mb-4">1. Get Your API Key</h3>
                <p className="text-gray-300 mb-4">
                  First, you'll need to get your API key from your profile page. This key authenticates your requests and tracks your token usage.
                </p>
                <CodeBlock
                  id="api-key"
                  title="Get API Key"
                  language="bash"
                  code={`# Get your API key from profile page
curl -X GET "https://api.vikki-ai.com/api/users/api-key" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN"`}
                />
              </div>

              <div>
                <h3 className="text-2xl font-bold text-white mb-4">2. Make Your First Request</h3>
                <p className="text-gray-300 mb-4">
                  Use your API key to make requests to our endpoints. All requests require the API key in the Authorization header.
                </p>
                <CodeBlock
                  id="first-request"
                  title="First API Call"
                  language="bash"
                  code={`curl -X POST "https://api.vikki-ai.com/api/generate" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "prompt": "Create a React todo component",
    "model": "basic"
  }'`}
                />
              </div>
            </div>
          </div>
        </div>

        {/* API Endpoints */}
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center gap-4 mb-8">
              <FiBook className="text-3xl text-blue-400" />
              <h2 className="text-3xl md:text-4xl font-bold text-white">API Endpoints</h2>
            </div>

            <div className="space-y-6">
              {endpoints.map((endpoint, index) => (
                <div key={index} className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <span className={`px-3 py-1 rounded-lg text-sm font-bold ${
                        endpoint.method === 'GET' ? 'bg-green-500/20 text-green-400' :
                        endpoint.method === 'POST' ? 'bg-blue-500/20 text-blue-400' :
                        'bg-yellow-500/20 text-yellow-400'
                      }`}>
                        {endpoint.method}
                      </span>
                      <code className="text-cyan-400 font-mono text-lg">{endpoint.path}</code>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-400">Tokens:</span>
                      <span className="text-yellow-400 font-bold">{endpoint.tokens}</span>
                    </div>
                  </div>
                  <p className="text-gray-300">{endpoint.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* IDE Integrations */}
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center gap-4 mb-8">
              <FiCode className="text-3xl text-green-400" />
              <h2 className="text-3xl md:text-4xl font-bold text-white">IDE Integrations</h2>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-1 gap-8">
              {ideIntegrations.map((ide, index) => (
                <div key={index} className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
                  <div className="flex items-center gap-4 mb-6">
                    <span className="text-3xl">{ide.icon}</span>
                    <div>
                      <h3 className="text-2xl font-bold text-white">{ide.name}</h3>
                      <p className="text-gray-300">{ide.description}</p>
                    </div>
                  </div>
                  <CodeBlock
                    id={`ide-${index}`}
                    title={`${ide.name} Integration`}
                    language={ide.name === 'IntelliJ IDEA' ? 'java' : ide.name === 'Sublime Text' ? 'python' : 'javascript'}
                    code={ide.code}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Rate Limits & Authentication */}
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Authentication</h3>
                <p className="text-gray-300 mb-4">
                  All API requests require authentication using your API key in the Authorization header.
                </p>
                <CodeBlock
                  id="auth-example"
                  title="Authentication Example"
                  language="javascript"
                  code={`const response = await fetch('https://api.vikki.ai/api/generate', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    prompt: 'Create a Vue.js component'
  })
});

const result = await response.json();`}
                />
              </div>

              <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-white mb-6">Rate Limits</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Free Plan</span>
                    <span className="text-cyan-400 font-bold">150 Tokens/Month</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Pro Plan</span>
                    <span className="text-blue-400 font-bold">500 Tokens/Month</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Enterprise Plan</span>
                    <span className="text-purple-400 font-bold">1000 Tokens/Month</span>
                  </div>
                </div>
                <p className="text-gray-400 text-sm mt-4">
                  Tokens refresh every 24 hours. Monitor your usage through the dashboard.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Support Section */}
        <div className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-cyan-900/20 to-blue-900/20 backdrop-blur-sm border border-cyan-500/20 rounded-3xl p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Need Help with Integration?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                Our developer support team is here to help you integrate VIKKI AI into your workflow.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105">
                  Contact Developer Support
                </button>
                <button className="border border-gray-600 hover:border-cyan-400 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:bg-cyan-400/10">
                  View Examples on GitHub
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default ApiDocs;
