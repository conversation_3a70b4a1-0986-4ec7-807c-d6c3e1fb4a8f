# Nginx Load Balancer Configuration for Backend Services

events {
    worker_connections 1024;
}

http {
    upstream backend_servers {
        least_conn;  # Load balancing method
        server backend:8000 max_fails=3 fail_timeout=30s;
        # Add more backend servers if scaling
        # server backend_2:8000 max_fails=3 fail_timeout=30s;
        # server backend_3:8000 max_fails=3 fail_timeout=30s;
    }

    server {
        listen 80;
        
        location / {
            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Health check
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
