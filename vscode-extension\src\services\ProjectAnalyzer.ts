import * as vscode from 'vscode';
import * as path from 'path';
import { VikkiAIProvider } from '../providers/VikkiAIProvider';

export class ProjectAnalyzer {
    constructor(private vikkiProvider: VikkiAIProvider) {}

    async analyzeCurrentProject(): Promise<void> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            vscode.window.showErrorMessage('No workspace folder found. Please open a project first.');
            return;
        }

        const progressOptions = {
            location: vscode.ProgressLocation.Notification,
            title: "VIKKI AI",
            cancellable: true
        };

        await vscode.window.withProgress(progressOptions, async (progress, token) => {
            try {
                progress.report({ message: "Scanning project files..." });

                // Get all relevant files in the project
                const files = await this.getProjectFiles(workspaceFolder.uri);
                
                if (files.length === 0) {
                    vscode.window.showWarningMessage('No code files found in the project.');
                    return;
                }

                if (token.isCancellationRequested) {
                    return;
                }

                progress.report({ 
                    message: `Analyzing ${files.length} files...`,
                    increment: 30
                });

                // Analyze the project
                const response = await this.vikkiProvider.analyzeProject({ files });

                if (token.isCancellationRequested) {
                    return;
                }

                progress.report({ 
                    message: "Generating analysis report...",
                    increment: 70
                });

                // Show analysis results
                await this.showAnalysisResults(response, workspaceFolder.name);

            } catch (error: any) {
                if (!token.isCancellationRequested) {
                    vscode.window.showErrorMessage(`Project analysis failed: ${error.message}`);
                }
            }
        });
    }

    async analyzeCurrentFile(editor: vscode.TextEditor): Promise<void> {
        const document = editor.document;
        
        if (document.isUntitled) {
            vscode.window.showErrorMessage('Please save the file before analyzing.');
            return;
        }

        try {
            const content = document.getText();
            const fileName = path.basename(document.fileName);
            
            const files = [{
                path: fileName,
                content: content
            }];

            const response = await this.vikkiProvider.analyzeProject({ files });
            await this.showAnalysisResults(response, fileName);

        } catch (error: any) {
            vscode.window.showErrorMessage(`File analysis failed: ${error.message}`);
        }
    }

    private async getProjectFiles(workspaceUri: vscode.Uri): Promise<Array<{path: string, content: string}>> {
        const files: Array<{path: string, content: string}> = [];
        const maxFiles = 50; // Limit to prevent overwhelming the API
        const maxFileSize = 100 * 1024; // 100KB limit per file

        // Define file patterns to include
        const includePatterns = [
            '**/*.{js,jsx,ts,tsx}',
            '**/*.{py,pyx}',
            '**/*.{java,kt}',
            '**/*.{cpp,c,h,hpp}',
            '**/*.{cs,vb}',
            '**/*.{php,rb,go,rs}',
            '**/*.{html,css,scss,sass}',
            '**/*.{json,yaml,yml,toml}',
            '**/*.{md,txt}',
            '**/package.json',
            '**/requirements.txt',
            '**/Cargo.toml',
            '**/pom.xml',
            '**/build.gradle',
            '**/*.csproj'
        ];

        // Define patterns to exclude
        const excludePatterns = [
            '**/node_modules/**',
            '**/venv/**',
            '**/env/**',
            '**/.git/**',
            '**/dist/**',
            '**/build/**',
            '**/target/**',
            '**/.next/**',
            '**/.nuxt/**',
            '**/coverage/**',
            '**/__pycache__/**',
            '**/*.min.js',
            '**/*.min.css',
            '**/vendor/**',
            '**/bower_components/**'
        ];

        try {
            for (const pattern of includePatterns) {
                if (files.length >= maxFiles) break;

                const foundFiles = await vscode.workspace.findFiles(
                    pattern,
                    `{${excludePatterns.join(',')}}`,
                    maxFiles - files.length
                );

                for (const fileUri of foundFiles) {
                    if (files.length >= maxFiles) break;

                    try {
                        const stat = await vscode.workspace.fs.stat(fileUri);
                        
                        // Skip files that are too large
                        if (stat.size > maxFileSize) {
                            continue;
                        }

                        const content = await vscode.workspace.fs.readFile(fileUri);
                        const textContent = Buffer.from(content).toString('utf8');
                        
                        // Skip binary files or files with non-text content
                        if (this.isBinaryContent(textContent)) {
                            continue;
                        }

                        const relativePath = vscode.workspace.asRelativePath(fileUri);
                        
                        files.push({
                            path: relativePath,
                            content: textContent
                        });

                    } catch (error) {
                        // Skip files that can't be read
                        console.warn(`Could not read file ${fileUri.fsPath}:`, error);
                        continue;
                    }
                }
            }
        } catch (error) {
            console.error('Error scanning project files:', error);
        }

        return files;
    }

    private isBinaryContent(content: string): boolean {
        // Simple heuristic to detect binary content
        const nonTextChars = content.match(/[\x00-\x08\x0E-\x1F\x7F]/g);
        return nonTextChars !== null && nonTextChars.length > content.length * 0.1;
    }

    private async showAnalysisResults(response: any, projectName: string): Promise<void> {
        // Create a comprehensive analysis report
        const report = this.generateAnalysisReport(response, projectName);
        
        // Create a new document with the analysis
        const doc = await vscode.workspace.openTextDocument({
            content: report,
            language: 'markdown'
        });
        
        await vscode.window.showTextDocument(doc);

        // Also show a summary notification
        const summary = this.generateSummary(response);
        const choice = await vscode.window.showInformationMessage(
            summary,
            'View Full Report',
            'Save Report',
            'Share Results'
        );

        switch (choice) {
            case 'View Full Report':
                // Already showing the report
                break;
            case 'Save Report':
                await this.saveReport(report, projectName);
                break;
            case 'Share Results':
                await this.shareResults(response);
                break;
        }
    }

    private generateAnalysisReport(response: any, projectName: string): string {
        const timestamp = new Date().toLocaleString();
        
        let report = `# VIKKI AI Project Analysis Report\n\n`;
        report += `**Project:** ${projectName}\n`;
        report += `**Analysis Date:** ${timestamp}\n`;
        report += `**Generated by:** VIKKI AI VS Code Extension\n\n`;

        // Analysis Overview
        if (response.analysis) {
            report += `## 📊 Analysis Overview\n\n`;
            report += `${JSON.stringify(response.analysis, null, 2)}\n\n`;
        }

        // Metrics
        if (response.metrics) {
            report += `## 📈 Code Metrics\n\n`;
            Object.entries(response.metrics).forEach(([key, value]) => {
                report += `- **${key}:** ${value}\n`;
            });
            report += `\n`;
        }

        // Issues Found
        if (response.issues && response.issues.length > 0) {
            report += `## ⚠️ Issues Found\n\n`;
            response.issues.forEach((issue: string, index: number) => {
                report += `${index + 1}. ${issue}\n`;
            });
            report += `\n`;
        }

        // Suggestions
        if (response.suggestions && response.suggestions.length > 0) {
            report += `## 💡 Suggestions for Improvement\n\n`;
            response.suggestions.forEach((suggestion: string, index: number) => {
                report += `${index + 1}. ${suggestion}\n`;
            });
            report += `\n`;
        }

        // Additional Analysis
        if (response.security_analysis) {
            report += `## 🔒 Security Analysis\n\n`;
            report += `${JSON.stringify(response.security_analysis, null, 2)}\n\n`;
        }

        if (response.performance_analysis) {
            report += `## ⚡ Performance Analysis\n\n`;
            report += `${JSON.stringify(response.performance_analysis, null, 2)}\n\n`;
        }

        if (response.code_quality) {
            report += `## ✨ Code Quality Assessment\n\n`;
            report += `${JSON.stringify(response.code_quality, null, 2)}\n\n`;
        }

        report += `---\n\n`;
        report += `*This report was generated by VIKKI AI. For more detailed analysis and recommendations, visit [vikki-ai.com](https://vikki-ai.com)*\n`;

        return report;
    }

    private generateSummary(response: any): string {
        let summary = 'Project analysis complete! ';
        
        const issueCount = response.issues?.length || 0;
        const suggestionCount = response.suggestions?.length || 0;
        
        if (issueCount > 0) {
            summary += `Found ${issueCount} issue${issueCount > 1 ? 's' : ''}. `;
        }
        
        if (suggestionCount > 0) {
            summary += `${suggestionCount} improvement suggestion${suggestionCount > 1 ? 's' : ''} available.`;
        }
        
        if (issueCount === 0 && suggestionCount === 0) {
            summary += 'No major issues found. Great work!';
        }

        return summary;
    }

    private async saveReport(report: string, projectName: string): Promise<void> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            vscode.window.showErrorMessage('No workspace folder found.');
            return;
        }

        const timestamp = new Date().toISOString().split('T')[0];
        const fileName = `vikki-ai-analysis-${projectName}-${timestamp}.md`;
        const filePath = path.join(workspaceFolder.uri.fsPath, fileName);
        const fileUri = vscode.Uri.file(filePath);

        try {
            await vscode.workspace.fs.writeFile(fileUri, Buffer.from(report, 'utf8'));
            vscode.window.showInformationMessage(`Analysis report saved as ${fileName}`);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to save report: ${error}`);
        }
    }

    private async shareResults(response: any): Promise<void> {
        const summary = this.generateSummary(response);
        const shareText = `VIKKI AI Project Analysis Results:\n\n${summary}\n\nGenerated with VIKKI AI VS Code Extension - https://vikki-ai.com`;
        
        await vscode.env.clipboard.writeText(shareText);
        vscode.window.showInformationMessage('Analysis summary copied to clipboard!');
    }
}
