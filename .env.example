# Environment Variables for Docker Compose
# Copy this file to .env and update the values

# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_key_secret_here
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret_here

# Stable Diffusion API (Optional)
STABILITY_API_KEY=your_stability_api_key_here

# MongoDB Configuration (for production)
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=your_secure_password_here

# JWT Secrets (change these in production)
JWT_SECRET=your_jwt_secret_here
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here

# Domain Configuration
DOMAIN=vikki-ai.com
FRONTEND_URL=https://vikki-ai.com
BACKEND_URL=https://api.vikki-ai.com

# SSL Configuration (for production)
SSL_EMAIL=<EMAIL>
