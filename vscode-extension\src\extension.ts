import * as vscode from 'vscode';
import { VikkiAIProvider } from './providers/VikkiAIProvider';
import { ChatViewProvider } from './providers/ChatViewProvider';
import { ProjectsViewProvider } from './providers/ProjectsViewProvider';
import { AuthViewProvider } from './providers/AuthViewProvider';
import { CodeGenerator } from './services/CodeGenerator';
import { ProjectAnalyzer } from './services/ProjectAnalyzer';
import { AuthenticationManager } from './services/AuthenticationManager';

export function activate(context: vscode.ExtensionContext) {
    console.log('VIKKI AI extension is now active!');

    // Initialize services
    const authManager = new AuthenticationManager(context);
    const vikkiProvider = new VikkiAIProvider(authManager);
    const codeGenerator = new CodeGenerator(vikkiProvider);
    const projectAnalyzer = new ProjectAnalyzer(vikkiProvider);

    // Initialize view providers
    const chatViewProvider = new ChatViewProvider(context, vikkiProvider);
    const projectsViewProvider = new ProjectsViewProvider(context, vikkiProvider);
    const authViewProvider = new AuthViewProvider(context, authManager);

    // Register view providers
    vscode.window.registerWebviewViewProvider('vikkiAI.chatView', chatViewProvider);
    vscode.window.registerWebviewViewProvider('vikkiAI.projectsView', projectsViewProvider);
    vscode.window.registerWebviewViewProvider('vikkiAI.authView', authViewProvider);

    // Set authentication context
    const updateAuthContext = async () => {
        const isAuthenticated = await authManager.isAuthenticated();
        vscode.commands.executeCommand('setContext', 'vikkiAI.authenticated', isAuthenticated);
    };
    updateAuthContext();

    // Register commands
    const commands = [
        // Authentication
        vscode.commands.registerCommand('vikkiAI.authenticate', async () => {
            await authManager.authenticate();
            updateAuthContext();
        }),

        // Code Generation
        vscode.commands.registerCommand('vikkiAI.generateCode', async () => {
            const prompt = await vscode.window.showInputBox({
                prompt: 'Enter your code generation prompt',
                placeHolder: 'e.g., Create a React component for user authentication'
            });
            
            if (prompt) {
                await codeGenerator.generateCode(prompt);
            }
        }),

        vscode.commands.registerCommand('vikkiAI.generateFromSelection', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No text selected');
                return;
            }

            const prompt = await vscode.window.showInputBox({
                prompt: 'What would you like to do with the selected code?',
                placeHolder: 'e.g., Refactor this function, Add error handling, Convert to TypeScript'
            });

            if (prompt) {
                await codeGenerator.generateFromSelection(selectedText, prompt, editor);
            }
        }),

        // Code Analysis
        vscode.commands.registerCommand('vikkiAI.analyzeProject', async () => {
            await projectAnalyzer.analyzeCurrentProject();
        }),

        vscode.commands.registerCommand('vikkiAI.analyzeFile', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }
            await projectAnalyzer.analyzeCurrentFile(editor);
        }),

        // Code Actions
        vscode.commands.registerCommand('vikkiAI.explainCode', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No text selected');
                return;
            }

            await codeGenerator.explainCode(selectedText);
        }),

        vscode.commands.registerCommand('vikkiAI.optimizeCode', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No text selected');
                return;
            }

            await codeGenerator.optimizeCode(selectedText, editor);
        }),

        vscode.commands.registerCommand('vikkiAI.generateTests', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No text selected');
                return;
            }

            await codeGenerator.generateTests(selectedText);
        }),

        vscode.commands.registerCommand('vikkiAI.generateDocumentation', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const selectedText = editor.document.getText(selection);
            
            if (!selectedText) {
                vscode.window.showErrorMessage('No text selected');
                return;
            }

            await codeGenerator.generateDocumentation(selectedText, editor);
        }),

        // Chat
        vscode.commands.registerCommand('vikkiAI.openChat', () => {
            vscode.commands.executeCommand('vikkiAI.chatView.focus');
        }),

        // Status
        vscode.commands.registerCommand('vikkiAI.showStatus', async () => {
            await authManager.showStatus();
        })
    ];

    // Add all commands to subscriptions
    commands.forEach(command => context.subscriptions.push(command));

    // Status bar item
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.command = 'vikkiAI.showStatus';
    
    const updateStatusBar = async () => {
        const isAuthenticated = await authManager.isAuthenticated();
        if (isAuthenticated) {
            statusBarItem.text = '$(robot) VIKKI AI';
            statusBarItem.tooltip = 'VIKKI AI - Connected';
            statusBarItem.backgroundColor = undefined;
        } else {
            statusBarItem.text = '$(robot) VIKKI AI (Not Connected)';
            statusBarItem.tooltip = 'VIKKI AI - Click to authenticate';
            statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
        }
        statusBarItem.show();
    };

    updateStatusBar();
    context.subscriptions.push(statusBarItem);

    // Listen for authentication changes
    authManager.onAuthenticationChanged(() => {
        updateAuthContext();
        updateStatusBar();
    });

    vscode.window.showInformationMessage('VIKKI AI extension activated! Use Ctrl+Shift+G to generate code.');
}

export function deactivate() {
    console.log('VIKKI AI extension is now deactivated');
}
