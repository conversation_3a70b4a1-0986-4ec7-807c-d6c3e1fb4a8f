{"name": "vikki-ai-extension", "displayName": "VIKKI AI - Code Generation & Analysis", "description": "AI-powered code generation, analysis, and chat directly in VS Code. Connect with your VIKKI AI account.", "version": "1.0.0", "publisher": "vikki-ai", "icon": "assets/icon.png", "engines": {"vscode": "^1.74.0"}, "categories": ["Machine Learning", "Programming Languages", "Snippets", "Other"], "keywords": ["ai", "code generation", "vikki", "artificial intelligence", "code analysis", "chat", "assistant"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "vikkiAI.authenticate", "title": "🔑 Set API Key", "category": "VIKKI AI"}, {"command": "vikkiAI.generateCode", "title": "✨ Generate Code", "category": "VIKKI AI"}, {"command": "vikkiAI.generateFromSelection", "title": "🎯 Generate from Selection", "category": "VIKKI AI"}, {"command": "vikkiAI.analyzeProject", "title": "🔍 Analyze Project", "category": "VIKKI AI"}, {"command": "vikkiAI.analyzeFile", "title": "📄 Analyze Current File", "category": "VIKKI AI"}, {"command": "vikkiAI.openChat", "title": "💬 Open Chat", "category": "VIKKI AI"}, {"command": "vikkiAI.explainCode", "title": "📖 Explain Code", "category": "VIKKI AI"}, {"command": "vikkiAI.optimizeCode", "title": "⚡ Optimize Code", "category": "VIKKI AI"}, {"command": "vikkiAI.generateTests", "title": "🧪 Generate Tests", "category": "VIKKI AI"}, {"command": "vikkiAI.generateDocumentation", "title": "📚 Generate Documentation", "category": "VIKKI AI"}, {"command": "vikkiAI.showStatus", "title": "📊 Show Status", "category": "VIKKI AI"}], "menus": {"editor/context": [{"submenu": "vikkiAI.submenu", "group": "navigation"}], "vikkiAI.submenu": [{"command": "vikkiAI.generateFromSelection", "when": "editorHasSelection", "group": "1_generate"}, {"command": "vikkiAI.explainCode", "when": "editorHasSelection", "group": "1_generate"}, {"command": "vikkiAI.optimizeCode", "when": "editorHasSelection", "group": "1_generate"}, {"command": "vikkiAI.generateTests", "when": "editorHasSelection", "group": "2_analysis"}, {"command": "vikkiAI.generateDocumentation", "when": "editorHasSelection", "group": "2_analysis"}, {"command": "vikkiAI.analyzeFile", "group": "2_analysis"}], "explorer/context": [{"command": "vikkiAI.analyzeProject", "group": "navigation"}], "commandPalette": [{"command": "vikkiAI.generateFromSelection", "when": "editorHasSelection"}]}, "submenus": [{"id": "vikkiAI.submenu", "label": "🤖 VIKKI AI"}], "views": {"vikkiAI": [{"id": "vikkiAI.chatView", "name": "Cha<PERSON>", "when": "vikkiAI.authenticated"}, {"id": "vikkiAI.projectsView", "name": "Generated Projects", "when": "vikkiAI.authenticated"}, {"id": "vikkiAI.authView", "name": "Authentication", "when": "!vikkiAI.authenticated"}]}, "viewsContainers": {"activitybar": [{"id": "vikkiAI", "title": "VIKKI AI", "icon": "$(robot)"}]}, "configuration": {"title": "VIKKI AI", "properties": {"vikkiAI.apiEndpoint": {"type": "string", "default": "https://vikki-ai.com/api", "description": "VIKKI AI API endpoint URL"}, "vikkiAI.autoSave": {"type": "boolean", "default": true, "description": "Automatically save generated files"}, "vikkiAI.showInlineComments": {"type": "boolean", "default": true, "description": "Show inline comments in generated code"}, "vikkiAI.defaultModel": {"type": "string", "default": "vikki", "enum": ["vikki", "vikki_advanced"], "description": "Default AI model to use"}, "vikkiAI.maxTokens": {"type": "number", "default": 2048, "description": "Maximum tokens for code generation"}}}, "keybindings": [{"command": "vikkiAI.generateCode", "key": "ctrl+shift+g", "mac": "cmd+shift+g"}, {"command": "vikkiAI.openChat", "key": "ctrl+shift+c", "mac": "cmd+shift+c"}, {"command": "vikkiAI.explainCode", "key": "ctrl+shift+e", "mac": "cmd+shift+e", "when": "editorHasSelection"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "publish": "vsce publish", "build": "node scripts/build.js", "dev-setup": "node scripts/install-dev.js", "clean": "rm -rf out dist *.vsix", "dev": "npm run watch"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0"}, "dependencies": {"axios": "^1.6.0", "marked": "^9.1.0"}, "repository": {"type": "git", "url": "https://github.com/vikki-ai/vscode-extension.git"}, "bugs": {"url": "https://github.com/vikki-ai/vscode-extension/issues"}, "homepage": "https://vikki-ai.com", "license": "MIT"}