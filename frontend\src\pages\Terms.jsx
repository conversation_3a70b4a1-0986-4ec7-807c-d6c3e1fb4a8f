import React from 'react';
import { FiFileText, FiUsers, FiDollarSign, FiAlertTriangle } from 'react-icons/fi';
import PageContainer from '../components/layout/PageContainer';

const Terms = () => {
  const sections = [
    {
      icon: <FiUsers className="text-cyan-400" />,
      title: "User Responsibilities",
      content: [
        "Provide accurate account information",
        "Use the service in compliance with applicable laws",
        "Respect intellectual property rights",
        "Not attempt to reverse engineer our AI models",
        "Report security vulnerabilities responsibly"
      ]
    },
    {
      icon: <FiFileText className="text-green-400" />,
      title: "Service Usage",
      content: [
        "Use VIKKI AI for legitimate development purposes",
        "Respect rate limits and token allocations",
        "Not share API keys or account credentials",
        "Not use the service to generate harmful content",
        "Comply with our acceptable use policy"
      ]
    },
    {
      icon: <FiDollarSign className="text-blue-400" />,
      title: "Billing & Payments",
      content: [
        "Subscription fees are billed in advance",
        "Refunds are provided according to our refund policy",
        "Price changes will be communicated 30 days in advance",
        "Unused tokens do not carry over between billing periods",
        "Payment disputes must be raised within 60 days"
      ]
    },
    {
      icon: <FiAlertTriangle className="text-yellow-400" />,
      title: "Limitations",
      content: [
        "Service is provided 'as is' without warranties",
        "We are not liable for generated code quality",
        "Maximum liability is limited to subscription fees paid",
        "Service availability is not guaranteed 100%",
        "AI-generated content may contain errors or biases"
      ]
    }
  ];

  return (
    <PageContainer>
      <div className="min-h-screen bg-gradient-to-br from-[#0a0a0f] via-[#1a1a2e] to-[#16213e] text-white pt-20">
        {/* Hero Section */}
        <div className="relative py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-3xl md:text-5xl lg:text-6xl font-black mb-8 tracking-tight leading-tight text-[#bbc0c0]">
              Terms of Service
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed">
              Please read these terms carefully before using VIKKI AI services.
            </p>
            <p className="text-lg text-gray-400 mt-4">
              Last updated: December 2024
            </p>
          </div>
        </div>

        {/* Terms Sections */}
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {sections.map((section, index) => (
                <div key={index} className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="text-3xl">
                      {section.icon}
                    </div>
                    <h2 className="text-2xl font-bold text-white">
                      {section.title}
                    </h2>
                  </div>
                  <ul className="space-y-3">
                    {section.content.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-300 leading-relaxed">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Detailed Terms */}
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 md:p-12">
              <h2 className="text-3xl font-bold text-white mb-8">Detailed Terms</h2>
              
              <div className="space-y-8">
                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">1. Acceptance of Terms</h3>
                  <p className="text-gray-300 leading-relaxed">
                    By accessing or using VIKKI AI, you agree to be bound by these Terms of Service and all applicable 
                    laws and regulations. If you do not agree with any of these terms, you are prohibited from using 
                    or accessing this service.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">2. Code Ownership and License</h3>
                  <p className="text-gray-300 leading-relaxed">
                    You retain full ownership of all code generated through VIKKI AI. We grant you a perpetual, 
                    worldwide, non-exclusive license to use, modify, and distribute the generated code for any 
                    commercial or personal purpose. You are responsible for ensuring the generated code meets 
                    your requirements and complies with applicable laws.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">3. Account Security</h3>
                  <p className="text-gray-300 leading-relaxed">
                    You are responsible for maintaining the confidentiality of your account credentials and for all 
                    activities that occur under your account. You must notify us immediately of any unauthorized 
                    use of your account or any other breach of security.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">4. Prohibited Uses</h3>
                  <p className="text-gray-300 leading-relaxed mb-4">
                    You may not use VIKKI AI for any of the following purposes:
                  </p>
                  <ul className="space-y-2 text-gray-300">
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span>Generating malicious code, malware, or security exploits</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span>Creating content that violates intellectual property rights</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span>Attempting to reverse engineer or replicate our AI models</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span>Sharing or reselling access to the service</span>
                    </li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">5. Service Availability</h3>
                  <p className="text-gray-300 leading-relaxed">
                    We strive to maintain high service availability but do not guarantee uninterrupted access. 
                    We may temporarily suspend the service for maintenance, updates, or other operational reasons. 
                    We will provide advance notice when possible.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">6. Termination</h3>
                  <p className="text-gray-300 leading-relaxed">
                    Either party may terminate this agreement at any time. Upon termination, your access to the 
                    service will cease, but you retain ownership of all previously generated code. We may terminate 
                    accounts that violate these terms without prior notice.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">7. Changes to Terms</h3>
                  <p className="text-gray-300 leading-relaxed">
                    We reserve the right to modify these terms at any time. Material changes will be communicated 
                    via email or through our service. Your continued use of VIKKI AI after such changes constitutes 
                    acceptance of the new terms.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-cyan-400 mb-4">8. Governing Law</h3>
                  <p className="text-gray-300 leading-relaxed">
                    These terms are governed by and construed in accordance with the laws of [Jurisdiction]. 
                    Any disputes arising from these terms will be resolved through binding arbitration.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-cyan-900/20 to-blue-900/20 backdrop-blur-sm border border-cyan-500/20 rounded-3xl p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Questions About These Terms?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                If you have any questions about these terms of service, please contact our legal team.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105">
                  Contact Legal Team
                </button>
                <button className="border border-gray-600 hover:border-cyan-400 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:bg-cyan-400/10">
                  View Previous Versions
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default Terms;
