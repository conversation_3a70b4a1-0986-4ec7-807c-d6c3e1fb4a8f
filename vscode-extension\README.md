# VIKKI AI - VS Code Extension

🤖 **AI-powered code generation, analysis, and chat directly in VS Code**

Connect your VS Code editor with VIKKI AI to supercharge your development workflow with intelligent code generation, project analysis, and interactive AI assistance.

## ✨ Features

### 🎯 **Intelligent Code Generation**
- Generate code from natural language prompts
- Context-aware suggestions based on your current project
- Support for multiple programming languages
- Generate complete projects or individual functions

### 🔍 **Project Analysis**
- Comprehensive code analysis and insights
- Security vulnerability detection
- Performance optimization suggestions
- Code quality assessment

### 💬 **Interactive AI Chat**
- Chat with VIKKI AI about your code
- Get explanations for complex code sections
- Ask questions about best practices
- Receive debugging assistance

### 🧪 **Code Enhancement**
- Automated test generation
- Documentation generation
- Code optimization suggestions
- Refactoring assistance

### 📊 **Project Management**
- View and manage generated projects
- Download projects as ZIP files
- Preview projects in browser
- Track your generation history

## 🚀 Getting Started

### 1. Installation
Install the extension from the VS Code Marketplace or search for "VIKKI AI" in the Extensions view.

### 2. Authentication
1. Get your API key from [vikki-ai.com/profile](https://vikki-ai.com/profile)
2. Open the VIKKI AI panel in the Activity Bar
3. Enter your API key in the Authentication view
4. Start generating code!

### 3. Usage

#### **Generate Code**
- Use `Ctrl+Shift+G` (or `Cmd+Shift+G` on Mac) to open the code generation prompt
- Type your request in natural language
- Watch as VIKKI AI generates the code for you

#### **Chat with AI**
- Use `Ctrl+Shift+C` (or `Cmd+Shift+C` on Mac) to open the chat panel
- Ask questions about your code or programming concepts
- Get instant AI-powered responses

#### **Analyze Code**
- Right-click on any file or selection
- Choose "VIKKI AI" > "Analyze" from the context menu
- Get detailed insights and suggestions

## 🎮 Commands

| Command | Shortcut | Description |
|---------|----------|-------------|
| `VIKKI AI: Generate Code` | `Ctrl+Shift+G` | Generate code from prompt |
| `VIKKI AI: Open Chat` | `Ctrl+Shift+C` | Open AI chat panel |
| `VIKKI AI: Explain Code` | `Ctrl+Shift+E` | Explain selected code |
| `VIKKI AI: Analyze Project` | - | Analyze entire project |
| `VIKKI AI: Generate Tests` | - | Generate tests for selected code |
| `VIKKI AI: Optimize Code` | - | Optimize selected code |
| `VIKKI AI: Set API Key` | - | Configure authentication |

## ⚙️ Configuration

Configure the extension through VS Code settings:

```json
{
  "vikkiAI.apiEndpoint": "https://vikki-ai.com/api",
  "vikkiAI.autoSave": true,
  "vikkiAI.showInlineComments": true,
  "vikkiAI.defaultModel": "vikki",
  "vikkiAI.maxTokens": 2048
}
```

## 🔧 Context Menu Integration

Right-click on any code selection to access VIKKI AI features:
- **Generate from Selection** - Modify or extend selected code
- **Explain Code** - Get detailed explanations
- **Optimize Code** - Improve performance and readability
- **Generate Tests** - Create unit tests
- **Generate Documentation** - Add comprehensive docs

## 📱 Panels

### **Chat Panel**
Interactive chat interface for:
- Code explanations
- Programming questions
- Debugging assistance
- Best practice advice

### **Projects Panel**
Manage your generated projects:
- View project history
- Download ZIP files
- Open project previews
- Track generation statistics

### **Authentication Panel**
Secure connection management:
- API key configuration
- Connection status
- Account information
- Usage statistics

## 🔐 Security & Privacy

- API keys are stored securely using VS Code's secret storage
- No code is stored on our servers without your explicit consent
- All communications are encrypted
- You maintain full control over your data

## 🆘 Support

### **Getting Help**
- Visit [vikki-ai.com/docs](https://vikki-ai.com/docs) for documentation
- Check our [FAQ](https://vikki-ai.com/faq) for common questions
- Contact support at [<EMAIL>](mailto:<EMAIL>)

### **Troubleshooting**

**Connection Issues:**
1. Verify your API key is correct
2. Check your internet connection
3. Ensure vikki-ai.com is accessible

**Generation Issues:**
1. Check your token balance at [vikki-ai.com/dashboard](https://vikki-ai.com/dashboard)
2. Try with a simpler prompt
3. Verify the selected programming language

**Performance Issues:**
1. Close unnecessary VS Code extensions
2. Restart VS Code
3. Clear extension cache

## 📈 Pricing

- **Free Plan**: 150 tokens for basic usage
- **Pro Plan**: $30/month for advanced features
- **Enterprise**: $299/user/month for teams

Get your API key and manage your subscription at [vikki-ai.com](https://vikki-ai.com).

## 🔄 Updates

The extension automatically updates to the latest version. Check the changelog for new features and improvements.

## 📄 License

This extension is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

---

**Made with ❤️ by the VIKKI AI Team**

[Website](https://vikki-ai.com) • [Documentation](https://vikki-ai.com/docs) • [Support](mailto:<EMAIL>) • [GitHub](https://github.com/vikki-ai/vscode-extension)
