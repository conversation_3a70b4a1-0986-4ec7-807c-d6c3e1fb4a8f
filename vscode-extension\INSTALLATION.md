# VIKKI AI VS Code Extension - Installation Guide

This guide will help you install, configure, and use the VIKKI AI VS Code extension.

## 📋 Prerequisites

### System Requirements
- **VS Code**: Version 1.74.0 or higher
- **Node.js**: Version 16.x or higher (for development)
- **Internet Connection**: Required for AI features
- **VIKKI AI Account**: Get one at [vikki-ai.com](https://vikki-ai.com)

### Get Your API Key
1. Visit [vikki-ai.com](https://vikki-ai.com)
2. Sign up or log in to your account
3. Go to your [Profile page](https://vikki-ai.com/profile)
4. Generate or copy your API key
5. Keep this key secure - you'll need it for the extension

## 🚀 Installation Methods

### Method 1: VS Code Marketplace (Recommended)
1. Open VS Code
2. Go to Extensions view (`Ctrl+Shift+X` or `Cmd+Shift+X`)
3. Search for "VIKKI AI"
4. Click "Install" on the official VIKKI AI extension
5. Reload VS Code when prompted

### Method 2: Install from VSIX File
1. Download the `.vsix` file from releases
2. Open VS Code
3. Go to Extensions view (`Ctrl+Shift+X`)
4. Click the "..." menu and select "Install from VSIX..."
5. Select the downloaded `.vsix` file
6. Reload VS Code when prompted

### Method 3: Development Installation
```bash
# Clone the repository
git clone https://github.com/vikki-ai/vscode-extension.git
cd vscode-extension

# Install dependencies and set up development environment
npm run dev-setup

# Build the extension
npm run build

# Install locally
code --install-extension *.vsix
```

## ⚙️ Configuration

### 1. Authentication Setup
After installation, you need to authenticate with VIKKI AI:

1. **Open VIKKI AI Panel**:
   - Click the robot icon (🤖) in the Activity Bar
   - Or use Command Palette: `Ctrl+Shift+P` → "VIKKI AI: Set API Key"

2. **Enter API Key**:
   - Paste your API key from vikki-ai.com/profile
   - Click "Connect to VIKKI AI"
   - Wait for successful authentication

3. **Verify Connection**:
   - Status bar should show "VIKKI AI" (connected)
   - Chat and Projects panels should be available

### 2. Extension Settings
Configure the extension through VS Code settings:

```json
{
  // API Configuration
  "vikkiAI.apiEndpoint": "https://vikki-ai.com/api",
  
  // Behavior Settings
  "vikkiAI.autoSave": true,
  "vikkiAI.showInlineComments": true,
  
  // AI Model Settings
  "vikkiAI.defaultModel": "vikki",
  "vikkiAI.maxTokens": 2048
}
```

**Setting Descriptions**:
- `apiEndpoint`: VIKKI AI API server URL
- `autoSave`: Automatically save generated files
- `showInlineComments`: Include explanatory comments in generated code
- `defaultModel`: Default AI model ("vikki" or "vikki_advanced")
- `maxTokens`: Maximum tokens for code generation

### 3. Keyboard Shortcuts
The extension comes with these default shortcuts:

| Action | Windows/Linux | Mac | Description |
|--------|---------------|-----|-------------|
| Generate Code | `Ctrl+Shift+G` | `Cmd+Shift+G` | Open code generation prompt |
| Open Chat | `Ctrl+Shift+C` | `Cmd+Shift+C` | Open AI chat panel |
| Explain Code | `Ctrl+Shift+E` | `Cmd+Shift+E` | Explain selected code |

**Customize Shortcuts**:
1. Go to File → Preferences → Keyboard Shortcuts
2. Search for "VIKKI AI"
3. Click the pencil icon to edit shortcuts

## 🎮 Usage Guide

### Basic Code Generation
1. **Open Command Palette**: `Ctrl+Shift+P`
2. **Type**: "VIKKI AI: Generate Code"
3. **Enter Prompt**: Describe what you want to create
4. **Wait**: AI generates and inserts code

**Example Prompts**:
- "Create a React component for user login"
- "Write a Python function to validate email addresses"
- "Generate a REST API endpoint for user management"

### Chat with AI
1. **Open Chat Panel**: Click robot icon → Chat tab
2. **Ask Questions**: Type your programming questions
3. **Get Answers**: Receive AI-powered responses
4. **Insert Code**: Click "Insert" on code blocks

### Code Analysis
1. **Select Code**: Highlight code you want to analyze
2. **Right-click**: Choose "VIKKI AI" from context menu
3. **Select Action**: 
   - Explain Code
   - Optimize Code
   - Generate Tests
   - Generate Documentation

### Project Management
1. **View Projects**: Open VIKKI AI panel → Projects tab
2. **Download**: Click download button on any project
3. **Preview**: Click preview to open in browser
4. **Manage**: View statistics and project history

## 🔧 Troubleshooting

### Common Issues

#### Authentication Problems
**Issue**: "Invalid API key" error
**Solutions**:
1. Verify API key is correct (copy from vikki-ai.com/profile)
2. Check internet connection
3. Ensure vikki-ai.com is accessible
4. Try regenerating API key on website

#### Connection Issues
**Issue**: "Cannot connect to VIKKI AI servers"
**Solutions**:
1. Check internet connection
2. Verify firewall/proxy settings
3. Try different network
4. Check vikki-ai.com status

#### Generation Issues
**Issue**: "Insufficient tokens" error
**Solutions**:
1. Check token balance at vikki-ai.com/dashboard
2. Upgrade plan if needed
3. Wait for token refresh (free plan)

#### Performance Issues
**Issue**: Slow response times
**Solutions**:
1. Check internet speed
2. Try simpler prompts
3. Close unnecessary VS Code extensions
4. Restart VS Code

### Debug Mode
Enable debug mode for detailed logging:

1. **Open Settings**: `Ctrl+,`
2. **Search**: "vikki"
3. **Enable**: Developer mode (if available)
4. **Check**: Output panel for logs

### Reset Extension
If you encounter persistent issues:

1. **Logout**: VIKKI AI panel → Settings → Logout
2. **Clear Cache**: Reload VS Code window
3. **Reinstall**: Uninstall and reinstall extension
4. **Reconfigure**: Set up API key again

## 📞 Support

### Getting Help
- **Documentation**: [vikki-ai.com/docs](https://vikki-ai.com/docs)
- **FAQ**: [vikki-ai.com/faq](https://vikki-ai.com/faq)
- **Email Support**: [<EMAIL>](mailto:<EMAIL>)
- **GitHub Issues**: [Extension Repository](https://github.com/vikki-ai/vscode-extension/issues)

### Reporting Bugs
When reporting issues, please include:
1. VS Code version
2. Extension version
3. Operating system
4. Error messages
5. Steps to reproduce
6. Expected vs actual behavior

### Feature Requests
Submit feature requests through:
- GitHub Issues (preferred)
- <NAME_EMAIL>
- Community forum at vikki-ai.com

## 🔄 Updates

### Automatic Updates
The extension updates automatically through VS Code's extension manager.

### Manual Updates
1. Go to Extensions view
2. Find VIKKI AI extension
3. Click "Update" if available
4. Reload VS Code

### Version History
Check [CHANGELOG.md](CHANGELOG.md) for detailed version history and new features.

## 🔐 Privacy & Security

### Data Handling
- API keys stored securely in VS Code's secret storage
- Code sent to VIKKI AI servers only when explicitly requested
- No automatic code collection or monitoring
- All communications encrypted (HTTPS)

### Best Practices
1. **Keep API Key Secret**: Never share or commit API keys
2. **Review Generated Code**: Always review AI-generated code before use
3. **Sensitive Data**: Avoid sending sensitive information in prompts
4. **Regular Updates**: Keep extension updated for security patches

---

**Need more help?** Visit [vikki-ai.com/support](https://vikki-ai.com/support) or contact [<EMAIL>](mailto:<EMAIL>)
