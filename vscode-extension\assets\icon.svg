<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="64" cy="64" r="60" fill="url(#gradient1)" stroke="url(#gradient2)" stroke-width="4"/>
  
  <!-- Robot Head -->
  <rect x="40" y="35" width="48" height="40" rx="8" fill="#ffffff" opacity="0.9"/>
  
  <!-- Eyes -->
  <circle cx="52" cy="50" r="4" fill="#007ACC"/>
  <circle cx="76" cy="50" r="4" fill="#007ACC"/>
  
  <!-- Antenna -->
  <line x1="64" y1="35" x2="64" y2="25" stroke="#007ACC" stroke-width="2" stroke-linecap="round"/>
  <circle cx="64" cy="25" r="3" fill="#007ACC"/>
  
  <!-- Mouth/Speaker -->
  <rect x="56" y="60" width="16" height="8" rx="4" fill="#007ACC" opacity="0.7"/>
  
  <!-- Body -->
  <rect x="45" y="75" width="38" height="35" rx="6" fill="#ffffff" opacity="0.9"/>
  
  <!-- Chest Panel -->
  <rect x="52" y="82" width="24" height="20" rx="3" fill="#007ACC" opacity="0.3"/>
  
  <!-- Code Symbols -->
  <text x="58" y="95" font-family="monospace" font-size="12" fill="#007ACC" font-weight="bold">&lt;/&gt;</text>
  
  <!-- Arms -->
  <rect x="30" y="80" width="12" height="6" rx="3" fill="#ffffff" opacity="0.8"/>
  <rect x="86" y="80" width="12" height="6" rx="3" fill="#ffffff" opacity="0.8"/>
  
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007ACC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#005a9e;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
    </linearGradient>
  </defs>
</svg>
