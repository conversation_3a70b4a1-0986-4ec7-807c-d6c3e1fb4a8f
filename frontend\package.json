{"name": "api-hub-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hcaptcha/react-hcaptcha": "^1.12.0", "@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "axios": "^1.6.7", "date-fns": "^4.1.0", "framer-motion": "^12.16.0", "lottie-react": "^2.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-router-dom": "^6.22.1", "react-syntax-highlighter": "^15.6.1"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^5.1.0"}}