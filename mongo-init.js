// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the vikki-ai database
db = db.getSiblingDB('vikki-ai');

// Create collections with proper indexes
db.createCollection('users');
db.createCollection('projects');
db.createCollection('password_resets');
db.createCollection('payments');
db.createCollection('subscriptions');

// Create indexes for better performance
db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "email": 1 }, { unique: true });
db.password_resets.createIndex({ "token": 1 }, { unique: true });
db.password_resets.createIndex({ "expires_at": 1 }, { expireAfterSeconds: 0 });
db.projects.createIndex({ "user_id": 1 });
db.projects.createIndex({ "created_at": -1 });

// Create a default admin user (optional)
// Note: In production, you should create users through the API
db.users.insertOne({
    username: "admin",
    email: "<EMAIL>",
    full_name: "System Administrator",
    hashed_password: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/SJx/6VO7u", // password: admin123
    disabled: false,
    plan: "enterprise",
    tokens: 1000,
    created_at: new Date(),
    last_login: null
});

print("Database initialization completed successfully!");
print("Created collections: users, projects, password_resets, payments, subscriptions");
print("Created indexes for performance optimization");
print("Default admin user created with username: admin, password: admin123");
print("Please change the default admin password in production!");
