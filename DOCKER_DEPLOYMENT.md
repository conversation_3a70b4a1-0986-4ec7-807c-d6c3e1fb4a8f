# Docker Deployment Guide for VIKKI AI

This guide explains how to deploy the VIKKI AI application using Docker and Docker Compose.

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM
- 10GB free disk space

## Quick Start

1. **Clone the repository and navigate to the project directory**
   ```bash
   cd /path/to/vikki-ai
   ```

2. **Create environment file**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` file with your actual values.

3. **Start the application**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Frontend: http://localhost (redirects to HTTPS)
   - Backend API: http://localhost:8000
   - MongoDB: localhost:27017

## Architecture

The deployment consists of three main services:

### 1. MongoDB Database (`mongodb`)
- **Image**: mongo:7.0
- **Port**: 27017
- **Data**: Persisted in Docker volume `mongodb_data`
- **Authentication**: admin/password123 (change in production)

### 2. Backend API (`backend`)
- **Build**: Custom Python FastAPI application
- **Port**: 8000
- **Dependencies**: MongoDB
- **Health Check**: `/health` endpoint

### 3. Frontend (`frontend`)
- **Build**: Multi-stage build (Node.js + Nginx)
- **Ports**: 80 (HTTP), 443 (HTTPS)
- **Features**: 
  - Nginx reverse proxy
  - SSL/TLS support
  - Gzip compression
  - Security headers
  - Rate limiting

## Configuration

### Environment Variables

Key environment variables in `docker-compose.yml`:

```yaml
# Backend Configuration
MONGODB_URI: Connection string for MongoDB
JWT_SECRET: Secret for JWT token signing
RAZORPAY_*: Payment gateway configuration
STABILITY_API_KEY: For AI image generation

# MongoDB Configuration
MONGO_INITDB_ROOT_USERNAME: Database admin username
MONGO_INITDB_ROOT_PASSWORD: Database admin password
```

### SSL/TLS Configuration

For production with SSL certificates:

1. **Place SSL certificates in `./ssl/` directory:**
   ```
   ssl/
   ├── fullchain.pem
   └── privkey.pem
   ```

2. **Update nginx.conf if needed for your domain**

3. **Use Let's Encrypt (recommended):**
   ```bash
   # Install certbot
   sudo apt install certbot

   # Get certificates
   sudo certbot certonly --standalone -d vikki-ai.com -d www.vikki-ai.com
   
   # Copy certificates
   sudo cp /etc/letsencrypt/live/vikki-ai.com/fullchain.pem ./ssl/
   sudo cp /etc/letsencrypt/live/vikki-ai.com/privkey.pem ./ssl/
   ```

## Commands

### Basic Operations

```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# View logs
docker-compose logs -f

# View logs for specific service
docker-compose logs -f backend

# Restart a service
docker-compose restart backend

# Rebuild and start
docker-compose up -d --build
```

### Database Operations

```bash
# Access MongoDB shell
docker-compose exec mongodb mongosh -u admin -p password123

# Backup database
docker-compose exec mongodb mongodump --uri="*********************************************************************" --out=/backup

# Restore database
docker-compose exec mongodb mongorestore --uri="*********************************************************************" /backup/vikki-ai
```

### Maintenance

```bash
# Update images
docker-compose pull

# Clean up unused resources
docker system prune -a

# View resource usage
docker stats

# Scale backend service
docker-compose up -d --scale backend=3
```

## Monitoring

### Health Checks

All services include health checks:

- **Backend**: `curl http://localhost:8000/health`
- **Frontend**: `curl http://localhost:80`
- **MongoDB**: Built-in MongoDB health check

### Logs

```bash
# All services
docker-compose logs -f

# Specific service with timestamps
docker-compose logs -f -t backend

# Last 100 lines
docker-compose logs --tail=100 frontend
```

## Production Deployment

### Security Checklist

1. **Change default passwords**
   - MongoDB admin password
   - JWT secrets
   - API keys

2. **Configure SSL/TLS**
   - Valid SSL certificates
   - HTTPS redirect
   - Security headers

3. **Network Security**
   - Firewall configuration
   - VPN access for admin
   - Rate limiting

4. **Backup Strategy**
   - Database backups
   - Volume backups
   - Configuration backups

### Performance Optimization

1. **Resource Limits**
   ```yaml
   services:
     backend:
       deploy:
         resources:
           limits:
             memory: 2G
             cpus: '1.0'
   ```

2. **Scaling**
   ```bash
   # Scale backend horizontally
   docker-compose up -d --scale backend=3
   ```

3. **Caching**
   - Redis for session storage
   - CDN for static assets
   - Database query optimization

## Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep :80
   
   # Change ports in docker-compose.yml
   ports:
     - "8080:80"  # Use different host port
   ```

2. **Permission issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER ./
   chmod -R 755 ./ssl
   ```

3. **Database connection issues**
   ```bash
   # Check MongoDB logs
   docker-compose logs mongodb
   
   # Test connection
   docker-compose exec backend python -c "import motor.motor_asyncio; print('OK')"
   ```

4. **Build failures**
   ```bash
   # Clean build
   docker-compose build --no-cache
   
   # Check disk space
   df -h
   ```

### Debug Mode

For development debugging:

```yaml
# In docker-compose.yml
services:
  backend:
    environment:
      - DEBUG=true
    volumes:
      - ./backend:/app  # Live code reload
```

## Support

For issues and questions:
- Check logs: `docker-compose logs`
- GitHub Issues: [Repository Issues](https://github.com/your-repo/issues)
- Email: <EMAIL>
